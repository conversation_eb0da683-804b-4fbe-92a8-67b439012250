#!/usr/bin/env python3
"""
US-09: API Protection Database Initialization
=============================================

This script initializes the database for the API protection feature,
creates tables, and optionally populates with sample data.

Usage:
    python us09_init_db.py [--create-rules] [--sample-data] [--reset]

Options:
    --create-rules    Create default security rules
    --sample-data     Create sample security data for testing
    --reset          Drop and recreate all tables (WARNING: destroys data)

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import uuid

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from us09_app import create_app
from us09_security_model import (
    db, SecurityEvent, SecurityEventType, SecurityEventSeverity,
    RateLimitRecord, APIKey, APIKeyStatus, AuditLog, SecurityRule
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def init_database(reset=False):
    """Initialize the database tables"""
    try:
        if reset:
            logger.warning("Dropping all tables...")
            db.drop_all()
        
        logger.info("Creating database tables...")
        db.create_all()
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise


def create_default_security_rules():
    """Create default security rules"""
    try:
        logger.info("Creating default security rules...")
        
        default_rules = [
            {
                'rule_name': 'Default Rate Limit',
                'rule_description': 'Default rate limiting for all endpoints',
                'rule_type': 'rate_limit',
                'is_enabled': True,
                'priority': 100,
                'max_requests': 1000,
                'time_window_seconds': 3600,
                'rule_config': {
                    'endpoints': ['*'],
                    'methods': ['*'],
                    'block_duration_seconds': 300
                }
            },
            {
                'rule_name': 'API Rate Limit',
                'rule_description': 'Rate limiting for API endpoints',
                'rule_type': 'rate_limit',
                'is_enabled': True,
                'priority': 90,
                'max_requests': 500,
                'time_window_seconds': 3600,
                'rule_config': {
                    'endpoints': ['/api/*'],
                    'methods': ['*'],
                    'block_duration_seconds': 600
                }
            },
            {
                'rule_name': 'Login Rate Limit',
                'rule_description': 'Rate limiting for login attempts',
                'rule_type': 'rate_limit',
                'is_enabled': True,
                'priority': 80,
                'max_requests': 5,
                'time_window_seconds': 300,
                'rule_config': {
                    'endpoints': ['/api/auth/login'],
                    'methods': ['POST'],
                    'block_duration_seconds': 900
                }
            },
            {
                'rule_name': 'SQL Injection Detection',
                'rule_description': 'Detect SQL injection attempts',
                'rule_type': 'pattern_match',
                'is_enabled': True,
                'priority': 70,
                'rule_config': {
                    'patterns': [
                        'union select',
                        'drop table',
                        'insert into',
                        'delete from',
                        'update set',
                        'exec(',
                        'execute(',
                        'sp_executesql'
                    ],
                    'case_sensitive': False,
                    'severity': 'HIGH',
                    'block': True
                }
            },
            {
                'rule_name': 'XSS Detection',
                'rule_description': 'Detect XSS attempts',
                'rule_type': 'pattern_match',
                'is_enabled': True,
                'priority': 60,
                'rule_config': {
                    'patterns': [
                        '<script',
                        'javascript:',
                        'onerror=',
                        'onload=',
                        'onclick=',
                        'onmouseover=',
                        'eval(',
                        'document.cookie'
                    ],
                    'case_sensitive': False,
                    'severity': 'MEDIUM',
                    'block': True
                }
            },
            {
                'rule_name': 'Bot Detection',
                'rule_description': 'Detect malicious bots and crawlers',
                'rule_type': 'user_agent_match',
                'is_enabled': True,
                'priority': 50,
                'rule_config': {
                    'patterns': [
                        'sqlmap',
                        'nikto',
                        'nmap',
                        'masscan',
                        'burp',
                        'owasp',
                        'w3af',
                        'acunetix'
                    ],
                    'case_sensitive': False,
                    'severity': 'HIGH',
                    'block': True
                }
            }
        ]
        
        for rule_data in default_rules:
            existing_rule = SecurityRule.query.filter_by(rule_name=rule_data['rule_name']).first()
            if not existing_rule:
                rule = SecurityRule(**rule_data)
                db.session.add(rule)
                logger.info(f"Created security rule: {rule_data['rule_name']}")
            else:
                logger.info(f"Security rule already exists: {rule_data['rule_name']}")
        
        db.session.commit()
        logger.info("Default security rules created successfully")
        
    except Exception as e:
        logger.error(f"Error creating default security rules: {str(e)}")
        db.session.rollback()
        raise


def create_sample_data():
    """Create sample security data for testing"""
    try:
        logger.info("Creating sample security data...")
        
        # Sample user IDs
        sample_users = [
            str(uuid.uuid4()),
            str(uuid.uuid4()),
            str(uuid.uuid4())
        ]
        
        # Create sample security events
        sample_events = [
            {
                'event_type': SecurityEventType.INVALID_TOKEN,
                'severity': SecurityEventSeverity.MEDIUM,
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'request_method': 'GET',
                'request_path': '/api/protected/basic',
                'event_description': 'Invalid JWT token provided',
                'threat_score': 0.6,
                'is_blocked': True
            },
            {
                'event_type': SecurityEventType.UNAUTHORIZED_ACCESS,
                'severity': SecurityEventSeverity.HIGH,
                'ip_address': '*********',
                'user_agent': 'curl/7.68.0',
                'request_method': 'POST',
                'request_path': '/api/protected/admin',
                'user_id': sample_users[0],
                'event_description': 'Attempted access to admin endpoint without admin privileges',
                'threat_score': 0.8,
                'is_blocked': True
            },
            {
                'event_type': SecurityEventType.RATE_LIMIT_EXCEEDED,
                'severity': SecurityEventSeverity.MEDIUM,
                'ip_address': '***********',
                'user_agent': 'Python-requests/2.28.1',
                'request_method': 'GET',
                'request_path': '/api/auth/check-token',
                'event_description': 'Rate limit exceeded for IP address',
                'threat_score': 0.5,
                'is_blocked': True
            },
            {
                'event_type': SecurityEventType.SQL_INJECTION_ATTEMPT,
                'severity': SecurityEventSeverity.CRITICAL,
                'ip_address': '************',
                'user_agent': 'sqlmap/1.6.7',
                'request_method': 'GET',
                'request_path': '/api/search?q=1 UNION SELECT * FROM users',
                'event_description': 'SQL injection attempt detected in query parameter',
                'threat_score': 0.95,
                'is_blocked': True
            },
            {
                'event_type': SecurityEventType.XSS_ATTEMPT,
                'severity': SecurityEventSeverity.HIGH,
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1)',
                'request_method': 'POST',
                'request_path': '/api/profile',
                'user_id': sample_users[1],
                'event_description': 'XSS attempt detected in request body',
                'threat_score': 0.85,
                'is_blocked': True
            }
        ]
        
        for event_data in sample_events:
            event = SecurityEvent(**event_data)
            db.session.add(event)
        
        # Create sample API keys
        sample_api_keys = [
            {
                'user_id': sample_users[0],
                'key_name': 'Development Key',
                'key_description': 'API key for development testing',
                'key_hash': 'hashed_key_1',
                'key_prefix': 'dr_dev_',
                'scopes': ['read', 'write'],
                'allowed_endpoints': ['/api/profile', '/api/preferences'],
                'rate_limit_per_hour': 100,
                'rate_limit_per_day': 1000,
                'status': APIKeyStatus.ACTIVE
            },
            {
                'user_id': sample_users[1],
                'key_name': 'Production Key',
                'key_description': 'API key for production use',
                'key_hash': 'hashed_key_2',
                'key_prefix': 'dr_prod_',
                'scopes': ['read'],
                'allowed_endpoints': ['/api/profile'],
                'rate_limit_per_hour': 1000,
                'rate_limit_per_day': 10000,
                'status': APIKeyStatus.ACTIVE,
                'expires_at': datetime.utcnow() + timedelta(days=365)
            }
        ]
        
        for key_data in sample_api_keys:
            api_key = APIKey(**key_data)
            db.session.add(api_key)
        
        # Create sample rate limit records
        sample_rate_limits = [
            {
                'identifier': '*************',
                'identifier_type': 'ip',
                'endpoint': '/api/auth/login',
                'method': 'POST',
                'request_count': 3,
                'window_start': datetime.utcnow() - timedelta(minutes=5),
                'window_end': datetime.utcnow() + timedelta(minutes=5),
                'max_requests': 5,
                'window_duration_seconds': 300,
                'is_exceeded': False
            },
            {
                'identifier': '************',
                'identifier_type': 'ip',
                'endpoint': '/api/search',
                'method': 'GET',
                'request_count': 1000,
                'window_start': datetime.utcnow() - timedelta(hours=1),
                'window_end': datetime.utcnow(),
                'max_requests': 500,
                'window_duration_seconds': 3600,
                'is_exceeded': True
            }
        ]
        
        for rate_limit_data in sample_rate_limits:
            rate_limit = RateLimitRecord(**rate_limit_data)
            db.session.add(rate_limit)
        
        # Create sample audit logs
        sample_audit_logs = [
            {
                'user_id': sample_users[0],
                'action': 'GET /api/auth/user-info',
                'resource_type': 'user_info',
                'request_method': 'GET',
                'request_path': '/api/auth/user-info',
                'response_status_code': 200,
                'success': True,
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            {
                'user_id': sample_users[1],
                'action': 'GET /api/protected/premium',
                'resource_type': 'premium_endpoint',
                'request_method': 'GET',
                'request_path': '/api/protected/premium',
                'response_status_code': 200,
                'success': True,
                'ip_address': '*********',
                'user_agent': 'Dr Resume Mobile App/1.0'
            },
            {
                'user_id': sample_users[2],
                'action': 'GET /api/protected/admin',
                'resource_type': 'admin_endpoint',
                'request_method': 'GET',
                'request_path': '/api/protected/admin',
                'response_status_code': 403,
                'success': False,
                'error_message': 'Admin access required',
                'ip_address': '***********',
                'user_agent': 'curl/7.68.0'
            }
        ]
        
        for audit_data in sample_audit_logs:
            audit_log = AuditLog(**audit_data)
            db.session.add(audit_log)
        
        db.session.commit()
        logger.info("Sample security data created successfully")
        
    except Exception as e:
        logger.error(f"Error creating sample data: {str(e)}")
        db.session.rollback()
        raise


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Initialize US-09 API Protection database')
    parser.add_argument('--create-rules', action='store_true', 
                       help='Create default security rules')
    parser.add_argument('--sample-data', action='store_true',
                       help='Create sample security data for testing')
    parser.add_argument('--reset', action='store_true',
                       help='Drop and recreate all tables (WARNING: destroys data)')
    
    args = parser.parse_args()
    
    # Create Flask app and initialize database
    app = create_app()
    
    with app.app_context():
        try:
            # Initialize database
            init_database(reset=args.reset)
            
            # Create default security rules if requested
            if args.create_rules:
                create_default_security_rules()
            
            # Create sample data if requested
            if args.sample_data:
                create_sample_data()
            
            logger.info("Database initialization completed successfully!")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            sys.exit(1)


if __name__ == '__main__':
    main()
