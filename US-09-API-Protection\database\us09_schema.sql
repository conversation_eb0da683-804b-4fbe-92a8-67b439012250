-- US-09: API Protection Database Schema
-- =====================================
-- 
-- This file contains the PostgreSQL database schema for the API protection feature
-- in the Dr. Resume application. It includes tables for security events, rate limiting,
-- API keys, audit logs, and security rules.
-- 
-- Author: Dr. Resume Development Team
-- Date: 2025-07-23
-- Version: 1.0.0

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for enums
CREATE TYPE security_event_type AS ENUM (
    'INVALID_TOKEN',
    'UNAUTHORIZED_ACCESS',
    'RATE_LIMIT_EXCEEDED',
    'SUSPICIOUS_REQUEST',
    'SQL_INJECTION_ATTEMPT',
    'XSS_ATTEMPT',
    'BRUTE_FORCE_ATTEMPT',
    'MALICIOUS_USER_AGENT',
    'BLOCKED_IP',
    'API_KEY_MISUSE'
);

CREATE TYPE security_event_severity AS ENUM (
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL'
);

CREATE TYPE api_key_status AS ENUM (
    'ACTIVE',
    'INACTIVE',
    'REVOKED',
    'EXPIRED'
);

-- Security Events Table
-- Stores all security-related events and incidents
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type security_event_type NOT NULL,
    severity security_event_severity NOT NULL,
    ip_address INET,
    user_agent TEXT,
    request_method VARCHAR(10),
    request_path TEXT,
    request_headers JSONB DEFAULT '{}',
    request_body TEXT,
    user_id UUID,
    session_id VARCHAR(255),
    event_description TEXT,
    event_data JSONB DEFAULT '{}',
    threat_score DECIMAL(3,2) DEFAULT 0.0,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_reason TEXT,
    geolocation JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_security_events_created_at (created_at),
    INDEX idx_security_events_ip_address (ip_address),
    INDEX idx_security_events_user_id (user_id),
    INDEX idx_security_events_event_type (event_type),
    INDEX idx_security_events_severity (severity),
    INDEX idx_security_events_is_blocked (is_blocked)
);

-- Rate Limit Records Table
-- Tracks rate limiting for different identifiers
CREATE TABLE rate_limit_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier VARCHAR(255) NOT NULL,
    identifier_type VARCHAR(50) NOT NULL, -- 'ip', 'user', 'api_key'
    endpoint VARCHAR(255),
    method VARCHAR(10),
    request_count INTEGER DEFAULT 0,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    window_end TIMESTAMP WITH TIME ZONE NOT NULL,
    max_requests INTEGER NOT NULL,
    window_duration_seconds INTEGER NOT NULL,
    is_exceeded BOOLEAN DEFAULT FALSE,
    first_request_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_request_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for rate limiting windows
    UNIQUE(identifier, identifier_type, endpoint, method, window_start),
    
    -- Indexes for performance
    INDEX idx_rate_limit_identifier (identifier, identifier_type),
    INDEX idx_rate_limit_window (window_start, window_end),
    INDEX idx_rate_limit_endpoint (endpoint),
    INDEX idx_rate_limit_exceeded (is_exceeded)
);

-- API Keys Table
-- Manages API keys for external access
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    key_description TEXT,
    key_hash VARCHAR(255) NOT NULL, -- Hashed API key
    key_prefix VARCHAR(20) NOT NULL, -- First few characters for identification
    scopes TEXT[] DEFAULT '{}', -- Array of allowed scopes
    allowed_endpoints TEXT[] DEFAULT '{}', -- Array of allowed endpoints
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    status api_key_status DEFAULT 'ACTIVE',
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    allowed_ips INET[] DEFAULT '{}', -- Array of allowed IP addresses
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_api_keys_user_id (user_id),
    INDEX idx_api_keys_key_hash (key_hash),
    INDEX idx_api_keys_key_prefix (key_prefix),
    INDEX idx_api_keys_status (status),
    INDEX idx_api_keys_expires_at (expires_at)
);

-- Audit Logs Table
-- Comprehensive audit trail for all API access
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    api_key_id UUID,
    ip_address INET,
    user_agent TEXT,
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    request_method VARCHAR(10),
    request_path TEXT,
    request_params JSONB DEFAULT '{}',
    request_body TEXT,
    response_status_code INTEGER,
    response_time_ms INTEGER,
    old_values JSONB DEFAULT '{}',
    new_values JSONB DEFAULT '{}',
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    session_id VARCHAR(255),
    correlation_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_audit_logs_user_id (user_id),
    INDEX idx_audit_logs_created_at (created_at),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_resource_type (resource_type),
    INDEX idx_audit_logs_success (success),
    INDEX idx_audit_logs_ip_address (ip_address)
);

-- Security Rules Table
-- Configurable security rules and policies
CREATE TABLE security_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name VARCHAR(100) NOT NULL UNIQUE,
    rule_description TEXT,
    rule_type VARCHAR(50) NOT NULL, -- 'rate_limit', 'ip_block', 'pattern_match', etc.
    is_enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 100,
    conditions JSONB NOT NULL DEFAULT '{}', -- Rule conditions
    actions JSONB NOT NULL DEFAULT '{}', -- Actions to take
    max_requests INTEGER,
    time_window_seconds INTEGER,
    block_duration_seconds INTEGER,
    rule_config JSONB DEFAULT '{}', -- Additional configuration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_security_rules_enabled (is_enabled),
    INDEX idx_security_rules_priority (priority),
    INDEX idx_security_rules_type (rule_type)
);

-- Blocked IPs Table
-- Tracks temporarily and permanently blocked IP addresses
CREATE TABLE blocked_ips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address INET NOT NULL UNIQUE,
    block_reason TEXT,
    block_type VARCHAR(20) DEFAULT 'TEMPORARY', -- 'TEMPORARY', 'PERMANENT'
    blocked_until TIMESTAMP WITH TIME ZONE,
    block_count INTEGER DEFAULT 1,
    first_blocked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_blocked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_rule_id UUID,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Foreign key constraints
    FOREIGN KEY (created_by_rule_id) REFERENCES security_rules(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_blocked_ips_ip_address (ip_address),
    INDEX idx_blocked_ips_blocked_until (blocked_until),
    INDEX idx_blocked_ips_is_active (is_active)
);

-- Security Metrics Table
-- Aggregated security metrics for monitoring
CREATE TABLE security_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_date DATE NOT NULL,
    total_requests INTEGER DEFAULT 0,
    blocked_requests INTEGER DEFAULT 0,
    threat_attempts INTEGER DEFAULT 0,
    unique_ips INTEGER DEFAULT 0,
    api_key_requests INTEGER DEFAULT 0,
    avg_threat_score DECIMAL(3,2) DEFAULT 0.0,
    top_threat_types JSONB DEFAULT '{}',
    top_attacking_ips JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for daily metrics
    UNIQUE(metric_date),
    
    -- Indexes for performance
    INDEX idx_security_metrics_date (metric_date)
);

-- Insert default security rules
INSERT INTO security_rules (rule_name, rule_description, rule_type, conditions, actions, max_requests, time_window_seconds) VALUES
('Default Rate Limit', 'Default rate limiting for all endpoints', 'rate_limit', 
 '{"endpoints": ["*"], "methods": ["*"]}', 
 '{"block": true, "log": true}', 
 1000, 3600),

('API Rate Limit', 'Rate limiting for API endpoints', 'rate_limit',
 '{"endpoints": ["/api/*"], "methods": ["*"]}',
 '{"block": true, "log": true}',
 500, 3600),

('Login Rate Limit', 'Rate limiting for login attempts', 'rate_limit',
 '{"endpoints": ["/api/auth/login"], "methods": ["POST"]}',
 '{"block": true, "log": true, "block_duration": 900}',
 5, 300),

('SQL Injection Detection', 'Detect SQL injection attempts', 'pattern_match',
 '{"patterns": ["union select", "drop table", "insert into", "delete from"], "case_sensitive": false}',
 '{"block": true, "log": true, "severity": "HIGH"}',
 NULL, NULL),

('XSS Detection', 'Detect XSS attempts', 'pattern_match',
 '{"patterns": ["<script", "javascript:", "onerror=", "onload="], "case_sensitive": false}',
 '{"block": true, "log": true, "severity": "MEDIUM"}',
 NULL, NULL);

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_security_events_composite 
ON security_events (created_at DESC, severity, event_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rate_limit_composite 
ON rate_limit_records (identifier, identifier_type, window_end);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_composite 
ON audit_logs (user_id, created_at DESC, success);

-- Create a function to clean up old records
CREATE OR REPLACE FUNCTION cleanup_old_security_data()
RETURNS void AS $$
BEGIN
    -- Delete security events older than 90 days
    DELETE FROM security_events 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    -- Delete rate limit records older than 7 days
    DELETE FROM rate_limit_records 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '7 days';
    
    -- Delete audit logs older than 1 year
    DELETE FROM audit_logs 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';
    
    -- Delete expired blocked IPs
    DELETE FROM blocked_ips 
    WHERE block_type = 'TEMPORARY' 
    AND blocked_until < CURRENT_TIMESTAMP;
    
    -- Delete old security metrics (keep 2 years)
    DELETE FROM security_metrics 
    WHERE metric_date < CURRENT_DATE - INTERVAL '2 years';
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to relevant tables
CREATE TRIGGER update_rate_limit_records_updated_at 
    BEFORE UPDATE ON rate_limit_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at 
    BEFORE UPDATE ON api_keys 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_rules_updated_at 
    BEFORE UPDATE ON security_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_metrics_updated_at 
    BEFORE UPDATE ON security_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO dr_resume_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO dr_resume_user;
