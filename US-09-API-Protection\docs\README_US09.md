# US-09: API Protection - Complete Guide

## 📋 Overview

**US-09: API Protection** implements comprehensive security measures for the Dr. Resume - AI Resume Scanner application. This user story provides enterprise-grade API protection including JWT authentication, role-based access control, threat detection, and security monitoring.

### 🎯 What This US Accomplishes

- ✅ **JWT Authentication**: Secure token-based authentication for all API endpoints
- ✅ **Role-based Access Control**: Premium and admin access restrictions
- ✅ **Threat Detection**: Real-time security monitoring and automatic blocking
- ✅ **Rate Limiting**: Intelligent request throttling and abuse prevention
- ✅ **Input Validation**: Comprehensive request sanitization and validation
- ✅ **Security Monitoring**: Detailed logging and audit trails
- ✅ **Auto-redirect Logic**: Frontend automatically handles token expiration
- ✅ **Premium UI Blocking**: UI elements hidden based on user role

## 🏗️ Architecture Overview

```
US-09-API-Protection/
├── backend/                           # Flask API security server
│   ├── us09_security_model.py        # Security database models
│   ├── us09_security_service.py      # Threat detection and analysis
│   ├── us09_security_middleware.py   # Security middleware and headers
│   ├── us09_auth_decorators.py       # JWT and role-based decorators
│   ├── us09_security_routes.py       # Security API endpoints
│   ├── us09_app.py                   # Main Flask application
│   └── requirements.txt              # Python dependencies
├── frontend/                         # Security demo interface
│   ├── us09_protection.html          # API protection demonstration
│   └── us09_protection.js            # JWT handling and role-based UI
├── database/                         # Database setup
│   ├── us09_schema.sql              # PostgreSQL security schema
│   └── us09_init_db.py              # Database initialization
├── tests/                           # Test suite
│   └── test_us09_protection.py      # Comprehensive security tests
└── docs/                           # Documentation
    └── README_US09.md              # This file
```

## 🚀 Quick Start Guide

### Prerequisites

1. **Python 3.9+** installed
2. **PostgreSQL 13+** running
3. **Redis** (for rate limiting and caching)
4. **Previous US completed** (US-01 through US-08)

### Step 1: Database Setup

1. **Ensure PostgreSQL is running** (from previous US setup)

2. **Run Database Schema**:
   ```bash
   cd US-09-API-Protection/database
   psql -U dr_resume_user -d dr_resume_db -f us09_schema.sql
   ```

3. **Initialize Security Database**:
   ```bash
   python us09_init_db.py --create-rules
   ```

### Step 2: Backend Setup

1. **Create Virtual Environment**:
   ```bash
   cd US-09-API-Protection/backend
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment Variables**:
   Create `.env` file in backend directory:
   ```env
   # Database Configuration (from previous US)
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=dr_resume_db
   DB_USER=dr_resume_user
   DB_PASSWORD=your_secure_password
   
   # Flask Configuration
   SECRET_KEY=your-super-secret-key-change-in-production
   JWT_SECRET_KEY=your-jwt-secret-key
   FLASK_ENV=development
   
   # Security Configuration
   SECURITY_ENABLED=True
   THREAT_DETECTION_ENABLED=True
   RATE_LIMITING_ENABLED=True
   AUTO_BLOCKING_ENABLED=True
   
   # Rate Limiting Configuration
   REDIS_URL=redis://localhost:6379
   RATELIMIT_DEFAULT=1000 per hour
   
   # Security Headers Configuration
   FORCE_HTTPS=False
   SESSION_COOKIE_SECURE=False
   
   # Audit Logging Configuration
   AUDIT_LOGGING_ENABLED=True
   SECURITY_EVENT_RETENTION_DAYS=90
   
   # CORS Configuration
   CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
   ```

4. **Start Backend Server**:
   ```bash
   python us09_app.py
   ```

   Server will start at: `http://localhost:5009`

### Step 3: Frontend Setup

1. **Open Frontend**:
   ```bash
   cd US-09-API-Protection/frontend
   
   # Using Python's built-in server
   python -m http.server 3000
   
   # Then open: http://localhost:3000/us09_protection.html
   ```

## 🧪 Testing

### Run Backend Tests

```bash
cd US-09-API-Protection/tests
pytest test_us09_protection.py -v
```

### Manual Testing Checklist

1. **JWT Authentication**:
   - [ ] Open `http://localhost:3000/us09_protection.html`
   - [ ] Test login with different user types (basic, premium, admin)
   - [ ] Verify token validation and expiration handling
   - [ ] Test auto-redirect on invalid token

2. **Role-based Access Control**:
   - [ ] Test basic protected route (requires valid JWT)
   - [ ] Test premium protected route (requires premium role)
   - [ ] Test admin protected route (requires admin role)
   - [ ] Verify UI elements hide/show based on role

3. **Security Features**:
   - [ ] Test rate limiting with multiple requests
   - [ ] Verify security headers in responses
   - [ ] Test input validation with malicious payloads
   - [ ] Check security event logging

4. **API Testing**:
   ```bash
   # Test basic protection
   curl -X GET http://localhost:5009/api/protected/basic \
     -H "Authorization: Bearer your-jwt-token"
   
   # Test premium protection
   curl -X GET http://localhost:5009/api/protected/premium \
     -H "Authorization: Bearer your-premium-jwt-token"
   
   # Test admin protection
   curl -X GET http://localhost:5009/api/protected/admin \
     -H "Authorization: Bearer your-admin-jwt-token"
   
   # Test token validation
   curl -X GET http://localhost:5009/api/auth/check-token \
     -H "Authorization: Bearer your-jwt-token"
   ```

## 📚 Learning Guide for Beginners

### Understanding JWT Protection Flow

1. **User requests protected resource** → Frontend checks for valid JWT token
2. **Token validation** → Backend verifies token signature and expiration
3. **Role checking** → System checks if user has required role for resource
4. **Access granted/denied** → Response sent based on authorization result
5. **Auto-redirect** → Frontend redirects to login if token invalid

### Key Concepts Explained

#### 1. **JWT Authentication Decorators**
```python
@jwt_required_with_protection()
def protected_route():
    user_id = get_jwt_identity()
    claims = get_jwt()
    # Route logic here
```

#### 2. **Role-based Access Control**
```python
@require_premium_access
def premium_feature():
    # Only users with premium role can access
    return premium_data
```

#### 3. **Frontend Token Management**
```javascript
// Check token validity
const tokenInfo = await checkTokenValidity();
if (!tokenInfo.valid) {
    redirectToLogin();
}

// Hide premium features for non-premium users
if (!tokenInfo.is_premium) {
    hidePremiumUI();
}
```

#### 4. **Security Middleware**
```python
class SecurityMiddleware:
    def before_request(self):
        # Validate input
        # Check rate limits
        # Detect threats
        # Log security events
```

## 🔧 Configuration Reference

### Environment Variables

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `SECURITY_ENABLED` | Enable security middleware | `True` | No |
| `THREAT_DETECTION_ENABLED` | Enable threat detection | `True` | No |
| `RATE_LIMITING_ENABLED` | Enable rate limiting | `True` | No |
| `REDIS_URL` | Redis URL for rate limiting | `redis://localhost:6379` | Yes |
| `FORCE_HTTPS` | Force HTTPS in production | `True` | No |

### API Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/protected/basic` | Basic protected route | JWT |
| `GET` | `/api/protected/premium` | Premium protected route | JWT + Premium |
| `GET` | `/api/protected/admin` | Admin protected route | JWT + Admin |
| `GET` | `/api/auth/check-token` | Token validation | None |
| `GET` | `/api/security/dashboard` | Security dashboard | JWT + Admin |

### JWT Claims Structure

```json
{
  "sub": "user-id-123",
  "role": "premium",
  "is_premium": true,
  "is_admin": false,
  "subscription_status": "active",
  "iat": 1642694400,
  "exp": 1642780800
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Token Invalid Errors**:
   ```
   Error: Invalid token
   ```
   **Solution**: Check JWT secret key configuration and token expiration

2. **Rate Limit Exceeded**:
   ```
   Error: Rate limit exceeded
   ```
   **Solution**: Check Redis connection and rate limit configuration

3. **Premium Access Denied**:
   ```
   Error: Premium subscription required
   ```
   **Solution**: Ensure JWT token includes `role: premium` or `is_premium: true`

4. **Security Headers Missing**:
   ```
   Warning: Security headers not applied
   ```
   **Solution**: Verify security middleware is properly initialized

## 🔄 Integration with Other US Features

US-09 protects all previous user stories:

- **US-01**: Enhanced login with JWT tokens
- **US-02**: Secure authentication endpoints
- **US-03**: Protected file upload endpoints
- **US-04**: Secured job description management
- **US-05**: Protected NLP processing endpoints
- **US-06**: Secured matching score calculations
- **US-07**: Premium-protected AI suggestions
- **US-08**: Secured dashboard and analytics

## 📖 Additional Resources

- [Flask-JWT-Extended Documentation](https://flask-jwt-extended.readthedocs.io/)
- [JWT.io - JWT Debugger](https://jwt.io/)
- [OWASP API Security Top 10](https://owasp.org/www-project-api-security/)
- [Flask Security Best Practices](https://flask.palletsprojects.com/en/2.3.x/security/)
- [Rate Limiting Strategies](https://cloud.google.com/architecture/rate-limiting-strategies-techniques)

---

**🎉 Congratulations!** You've successfully implemented US-09: API Protection with comprehensive JWT authentication, role-based access control, and enterprise-grade security features. Your API is now secure and ready for production use!
