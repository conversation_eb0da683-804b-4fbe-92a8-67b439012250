"""
US-09: API Protection Test Suite
================================

Comprehensive test suite for the API protection feature in the Dr. Resume application.
Tests JWT authentication, role-based access control, security middleware, and threat detection.

Test Categories:
- JWT authentication tests
- Role-based access control tests
- Security middleware tests
- Threat detection tests
- Rate limiting tests

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import pytest
import json
import uuid
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Flask and testing imports
from flask import Flask
from flask_testing import TestCase
from flask_jwt_extended import create_access_token

# Local imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from us09_app import create_app
from us09_security_model import (
    db, SecurityEvent, SecurityEventType, SecurityEventSeverity,
    RateLimitRecord, APIKey, AuditLog, SecurityRule
)
from us09_auth_decorators import (
    jwt_required_with_protection, require_premium_access, require_admin_access,
    check_token_validity
)
from us09_security_service import ThreatDetector, RateLimiter, InputValidator


class APIProtectionTestCase(TestCase):
    """Base test case for API protection tests"""
    
    def create_app(self):
        """Create test Flask application"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        app.config['SECURITY_ENABLED'] = True
        return app
    
    def setUp(self):
        """Set up test database and sample data"""
        db.create_all()
        self.create_sample_data()
    
    def tearDown(self):
        """Clean up test database"""
        db.session.remove()
        db.drop_all()
    
    def create_sample_data(self):
        """Create sample data for testing"""
        self.sample_user_id = str(uuid.uuid4())
        self.admin_user_id = str(uuid.uuid4())
        self.premium_user_id = str(uuid.uuid4())
    
    def create_test_token(self, user_id=None, role='basic', is_premium=False, is_admin=False):
        """Create test JWT token"""
        user_id = user_id or self.sample_user_id
        additional_claims = {
            'role': role,
            'is_premium': is_premium,
            'is_admin': is_admin
        }
        
        with self.app.app_context():
            token = create_access_token(
                identity=user_id,
                additional_claims=additional_claims
            )
        return token
    
    def get_auth_headers(self, token=None):
        """Get authentication headers for testing"""
        if token is None:
            token = self.create_test_token()
        
        return {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }


class TestJWTAuthentication(APIProtectionTestCase):
    """Test JWT authentication functionality"""
    
    def test_valid_token_access(self):
        """Test access with valid JWT token"""
        token = self.create_test_token()
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/basic', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['protection_level'], 'basic')
    
    def test_invalid_token_access(self):
        """Test access with invalid JWT token"""
        headers = {
            'Authorization': 'Bearer invalid-token',
            'Content-Type': 'application/json'
        }
        
        response = self.client.get('/api/protected/basic', headers=headers)
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertEqual(data['error'], 'invalid_token')
    
    def test_missing_token_access(self):
        """Test access without JWT token"""
        response = self.client.get('/api/protected/basic')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertEqual(data['error'], 'invalid_token')
    
    def test_expired_token_access(self):
        """Test access with expired JWT token"""
        # Create token that expires immediately
        with self.app.app_context():
            token = create_access_token(
                identity=self.sample_user_id,
                expires_delta=timedelta(seconds=-1)  # Already expired
            )
        
        headers = self.get_auth_headers(token)
        response = self.client.get('/api/protected/basic', headers=headers)
        
        self.assertEqual(response.status_code, 401)
    
    def test_token_validation_endpoint(self):
        """Test token validation endpoint"""
        token = self.create_test_token(role='premium', is_premium=True)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/auth/check-token', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertTrue(data['data']['valid'])
        self.assertTrue(data['data']['is_premium'])


class TestRoleBasedAccessControl(APIProtectionTestCase):
    """Test role-based access control"""
    
    def test_basic_user_access_basic_route(self):
        """Test basic user accessing basic route"""
        token = self.create_test_token(role='basic')
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/basic', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    def test_basic_user_access_premium_route(self):
        """Test basic user accessing premium route (should fail)"""
        token = self.create_test_token(role='basic', is_premium=False)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/premium', headers=headers)
        
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertEqual(data['error'], 'premium_required')
        self.assertTrue(data['upgrade_required'])
    
    def test_premium_user_access_premium_route(self):
        """Test premium user accessing premium route"""
        token = self.create_test_token(role='premium', is_premium=True)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/premium', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['protection_level'], 'premium')
        self.assertIn('premium_features', data)
    
    def test_basic_user_access_admin_route(self):
        """Test basic user accessing admin route (should fail)"""
        token = self.create_test_token(role='basic')
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/admin', headers=headers)
        
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertEqual(data['error'], 'admin_required')
    
    def test_admin_user_access_admin_route(self):
        """Test admin user accessing admin route"""
        token = self.create_test_token(role='admin', is_admin=True)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/admin', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['protection_level'], 'admin')
        self.assertIn('admin_features', data)
    
    def test_user_info_endpoint(self):
        """Test user info endpoint with different roles"""
        # Test premium user
        token = self.create_test_token(role='premium', is_premium=True)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/auth/user-info', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertTrue(data['data']['is_premium'])
        self.assertFalse(data['data']['is_admin'])


class TestThreatDetector(APIProtectionTestCase):
    """Test threat detection functionality"""
    
    def setUp(self):
        super().setUp()
        self.threat_detector = ThreatDetector()
    
    def test_sql_injection_detection(self):
        """Test SQL injection pattern detection"""
        malicious_request = {
            'url': '/api/test?id=1 OR 1=1',
            'body': '',
            'headers': {},
            'user_agent': 'test-agent'
        }
        
        result = self.threat_detector.analyze_request(malicious_request)
        
        self.assertGreater(result['threat_score'], 0.5)
        self.assertIn('sql_injection_in_url', result['threats_detected'])
        self.assertTrue(result['should_block'])
    
    def test_xss_detection(self):
        """Test XSS pattern detection"""
        malicious_request = {
            'url': '/api/test',
            'body': '<script>alert("xss")</script>',
            'headers': {},
            'user_agent': 'test-agent'
        }
        
        result = self.threat_detector.analyze_request(malicious_request)
        
        self.assertGreater(result['threat_score'], 0.5)
        self.assertIn('xss_in_body', result['threats_detected'])
    
    def test_suspicious_user_agent_detection(self):
        """Test suspicious user agent detection"""
        malicious_request = {
            'url': '/api/test',
            'body': '',
            'headers': {},
            'user_agent': 'sqlmap/1.0'
        }
        
        result = self.threat_detector.analyze_request(malicious_request)
        
        self.assertGreater(result['threat_score'], 0.2)
        self.assertIn('bot_user_agent', result['threats_detected'])
    
    def test_clean_request(self):
        """Test clean request analysis"""
        clean_request = {
            'url': '/api/profile',
            'body': '{"name": "John Doe"}',
            'headers': {'Content-Type': 'application/json'},
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        result = self.threat_detector.analyze_request(clean_request)
        
        self.assertLess(result['threat_score'], 0.3)
        self.assertFalse(result['should_block'])


class TestRateLimiter(APIProtectionTestCase):
    """Test rate limiting functionality"""
    
    def setUp(self):
        super().setUp()
        self.rate_limiter = RateLimiter()
    
    def test_rate_limit_within_bounds(self):
        """Test request within rate limits"""
        result = self.rate_limiter.check_rate_limit(
            identifier='test-ip',
            identifier_type='ip',
            endpoint='/api/test',
            method='GET'
        )
        
        self.assertTrue(result['allowed'])
        self.assertFalse(result['limit_exceeded'])
    
    def test_rate_limit_exceeded(self):
        """Test rate limit exceeded scenario"""
        # Create a rate limit record that's already exceeded
        rate_record = RateLimitRecord(
            identifier='test-ip-exceeded',
            identifier_type='ip',
            endpoint='/api/test',
            method='GET',
            request_count=1000,
            window_start=datetime.utcnow(),
            window_end=datetime.utcnow() + timedelta(hours=1),
            max_requests=100,
            window_duration_seconds=3600,
            is_exceeded=True
        )
        db.session.add(rate_record)
        db.session.commit()
        
        result = self.rate_limiter.check_rate_limit(
            identifier='test-ip-exceeded',
            identifier_type='ip',
            endpoint='/api/test',
            method='GET'
        )
        
        self.assertFalse(result['allowed'])
        self.assertTrue(result['limit_exceeded'])
        self.assertIn('retry_after', result)


class TestInputValidator(APIProtectionTestCase):
    """Test input validation functionality"""
    
    def setUp(self):
        super().setUp()
        self.input_validator = InputValidator()
    
    def test_valid_json_validation(self):
        """Test validation of clean JSON data"""
        clean_data = {
            'json': {
                'name': 'John Doe',
                'email': '<EMAIL>',
                'age': 30
            },
            'params': {'page': '1'},
            'headers': {'Content-Type': 'application/json'}
        }
        
        result = self.input_validator.validate_request(clean_data)
        
        self.assertTrue(result['is_valid'])
        self.assertEqual(len(result['errors']), 0)
    
    def test_malicious_json_validation(self):
        """Test validation of malicious JSON data"""
        malicious_data = {
            'json': {
                'name': '<script>alert("xss")</script>',
                'comment': 'A' * 2000  # Too long
            },
            'params': {},
            'headers': {}
        }
        
        result = self.input_validator.validate_request(malicious_data)
        
        self.assertFalse(result['is_valid'])
        self.assertGreater(len(result['errors']), 0)
    
    def test_parameter_validation(self):
        """Test query parameter validation"""
        malicious_params = {
            'json': {},
            'params': {
                'search': '<script>alert("xss")</script>',
                'filter': 'normal_value'
            },
            'headers': {}
        }
        
        result = self.input_validator.validate_request(malicious_params)
        
        self.assertFalse(result['is_valid'])
        self.assertIn('dangerous characters', str(result['errors']))


class TestSecurityMiddleware(APIProtectionTestCase):
    """Test security middleware functionality"""
    
    def test_security_headers_applied(self):
        """Test that security headers are applied to responses"""
        token = self.create_test_token()
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/basic', headers=headers)
        
        # Check for security headers
        self.assertIn('X-Content-Type-Options', response.headers)
        self.assertIn('X-Frame-Options', response.headers)
        self.assertIn('X-XSS-Protection', response.headers)
        self.assertIn('Content-Security-Policy', response.headers)
        
        self.assertEqual(response.headers['X-Content-Type-Options'], 'nosniff')
        self.assertEqual(response.headers['X-Frame-Options'], 'DENY')
    
    def test_response_time_header(self):
        """Test that response time header is added"""
        token = self.create_test_token()
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/protected/basic', headers=headers)
        
        self.assertIn('X-Response-Time', response.headers)
        self.assertTrue(response.headers['X-Response-Time'].endswith('ms'))


class TestSecurityEventLogging(APIProtectionTestCase):
    """Test security event logging"""
    
    def test_security_event_creation(self):
        """Test creation of security events"""
        # Make a request that should trigger security logging
        headers = {
            'Authorization': 'Bearer invalid-token',
            'Content-Type': 'application/json'
        }
        
        response = self.client.get('/api/protected/basic', headers=headers)
        
        # Check that security event was logged
        events = SecurityEvent.query.filter_by(
            event_type=SecurityEventType.INVALID_TOKEN
        ).all()
        
        self.assertGreater(len(events), 0)
        
        event = events[0]
        self.assertEqual(event.event_type, SecurityEventType.INVALID_TOKEN)
        self.assertEqual(event.severity, SecurityEventSeverity.MEDIUM)
    
    def test_audit_log_creation(self):
        """Test creation of audit logs"""
        token = self.create_test_token()
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/auth/user-info', headers=headers)
        
        # Check that audit log was created
        audit_logs = AuditLog.query.filter_by(
            user_id=self.sample_user_id
        ).all()
        
        self.assertGreater(len(audit_logs), 0)
        
        log = audit_logs[0]
        self.assertEqual(log.user_id, self.sample_user_id)
        self.assertTrue(log.success)


class TestHealthEndpoints(APIProtectionTestCase):
    """Test health check endpoints"""
    
    def test_health_endpoint(self):
        """Test main health endpoint"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('version', data)
        self.assertIn('security_features', data)
    
    def test_api_health_endpoint(self):
        """Test API health endpoint"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('security_statistics', data)
        self.assertIn('endpoints', data)


class TestDevelopmentEndpoints(APIProtectionTestCase):
    """Test development-only endpoints"""
    
    def test_create_test_token_endpoint(self):
        """Test test token creation endpoint"""
        response = self.client.post('/api/dev/create-test-token', 
                                  json={
                                      'user_id': 'test-user',
                                      'is_premium': True
                                  })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('token', data)
        self.assertTrue(data['is_premium'])


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
