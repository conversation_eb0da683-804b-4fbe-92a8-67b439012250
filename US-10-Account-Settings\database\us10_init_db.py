#!/usr/bin/env python3
"""
US-10: Account Settings Database Initialization
===============================================

This script initializes the database for the account settings feature,
creates tables, and optionally populates with sample data.

Usage:
    python us10_init_db.py [--sample-data] [--reset]

Options:
    --sample-data     Create sample account data for testing
    --reset          Drop and recreate all tables (WARNING: destroys data)

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import uuid

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from us10_app import create_app
from us10_account_model import (
    db, UserProfile, UserPreferences, SubscriptionPlan, UserSubscription,
    AccountActivity, ActivityType, SubscriptionStatus
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def init_database(reset=False):
    """Initialize the database tables"""
    try:
        if reset:
            logger.warning("Dropping all tables...")
            db.drop_all()
        
        logger.info("Creating database tables...")
        db.create_all()
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise


def create_sample_data():
    """Create sample account data for testing"""
    try:
        logger.info("Creating sample account data...")
        
        # Sample user IDs (these would normally come from US-01 users table)
        sample_users = [
            {
                'user_id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'role': 'basic'
            },
            {
                'user_id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'role': 'premium'
            },
            {
                'user_id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'role': 'admin'
            }
        ]
        
        # Create sample user profiles
        sample_profiles = [
            {
                'user_id': sample_users[0]['user_id'],
                'first_name': 'John',
                'last_name': 'Doe',
                'display_name': 'John Doe',
                'bio': 'Experienced software developer with 5 years in full-stack development. Passionate about creating efficient and scalable solutions.',
                'phone_number': '******-0123',
                'country': 'US',
                'timezone': 'America/New_York',
                'language': 'en',
                'job_title': 'Senior Software Developer',
                'company': 'TechCorp Inc.',
                'industry': 'Technology',
                'experience_years': 5,
                'linkedin_url': 'https://linkedin.com/in/johndoe',
                'github_url': 'https://github.com/johndoe',
                'portfolio_url': 'https://johndoe.dev'
            },
            {
                'user_id': sample_users[1]['user_id'],
                'first_name': 'Jane',
                'last_name': 'Smith',
                'display_name': 'Jane Smith',
                'bio': 'Product manager with expertise in agile methodologies and user experience design. Leading cross-functional teams to deliver innovative products.',
                'phone_number': '******-0456',
                'country': 'CA',
                'timezone': 'America/Toronto',
                'language': 'en',
                'job_title': 'Senior Product Manager',
                'company': 'InnovateLabs',
                'industry': 'Technology',
                'experience_years': 8,
                'linkedin_url': 'https://linkedin.com/in/janesmith',
                'portfolio_url': 'https://janesmith.com'
            },
            {
                'user_id': sample_users[2]['user_id'],
                'first_name': 'Admin',
                'last_name': 'User',
                'display_name': 'Dr. Resume Admin',
                'bio': 'System administrator for Dr. Resume platform.',
                'country': 'US',
                'timezone': 'America/Los_Angeles',
                'language': 'en',
                'job_title': 'System Administrator',
                'company': 'Dr. Resume',
                'industry': 'Technology'
            }
        ]
        
        for profile_data in sample_profiles:
            profile = UserProfile(**profile_data)
            db.session.add(profile)
            logger.info(f"Created profile for: {profile_data['display_name']}")
        
        # Create sample user preferences
        sample_preferences = [
            {
                'user_id': sample_users[0]['user_id'],
                'email_notifications': True,
                'marketing_emails': False,
                'security_alerts': True,
                'scan_completion_notifications': True,
                'weekly_reports': True,
                'profile_visibility': 'private',
                'data_sharing': False,
                'analytics_tracking': True,
                'theme': 'light',
                'dashboard_layout': 'default',
                'items_per_page': 20,
                'auto_save': True,
                'ai_suggestions_enabled': True,
                'processing_quality': 'standard',
                'auto_keyword_extraction': True,
                'two_factor_enabled': False,
                'session_timeout_minutes': 60,
                'login_notifications': True
            },
            {
                'user_id': sample_users[1]['user_id'],
                'email_notifications': True,
                'marketing_emails': True,
                'security_alerts': True,
                'scan_completion_notifications': True,
                'weekly_reports': True,
                'profile_visibility': 'public',
                'data_sharing': True,
                'analytics_tracking': True,
                'theme': 'dark',
                'dashboard_layout': 'compact',
                'items_per_page': 50,
                'auto_save': True,
                'ai_suggestions_enabled': True,
                'processing_quality': 'detailed',
                'auto_keyword_extraction': True,
                'two_factor_enabled': True,
                'session_timeout_minutes': 120,
                'login_notifications': True
            },
            {
                'user_id': sample_users[2]['user_id'],
                'email_notifications': True,
                'marketing_emails': False,
                'security_alerts': True,
                'scan_completion_notifications': False,
                'weekly_reports': False,
                'profile_visibility': 'private',
                'data_sharing': False,
                'analytics_tracking': False,
                'theme': 'auto',
                'dashboard_layout': 'admin',
                'items_per_page': 100,
                'auto_save': True,
                'ai_suggestions_enabled': True,
                'processing_quality': 'detailed',
                'auto_keyword_extraction': True,
                'two_factor_enabled': True,
                'session_timeout_minutes': 30,
                'login_notifications': True
            }
        ]
        
        for pref_data in sample_preferences:
            preferences = UserPreferences(**pref_data)
            db.session.add(preferences)
            logger.info(f"Created preferences for user: {pref_data['user_id']}")
        
        # Get subscription plans (should already exist from app initialization)
        basic_plan = SubscriptionPlan.query.filter_by(name='Basic').first()
        premium_plan = SubscriptionPlan.query.filter_by(name='Premium').first()
        
        if not basic_plan or not premium_plan:
            logger.warning("Subscription plans not found, creating them...")
            # Create plans if they don't exist
            if not basic_plan:
                basic_plan = SubscriptionPlan(
                    name='Basic',
                    description='Essential resume scanning features',
                    price_monthly=0.00,
                    price_yearly=0.00,
                    max_scans_per_month=5,
                    ai_suggestions_included=False,
                    features=['Basic resume scanning', 'Keyword matching', 'Basic suggestions']
                )
                db.session.add(basic_plan)
            
            if not premium_plan:
                premium_plan = SubscriptionPlan(
                    name='Premium',
                    description='Advanced features with AI-powered suggestions',
                    price_monthly=9.99,
                    price_yearly=99.99,
                    max_scans_per_month=50,
                    ai_suggestions_included=True,
                    premium_templates=True,
                    priority_support=True,
                    advanced_analytics=True,
                    export_capabilities=True,
                    features=[
                        'Unlimited resume scanning',
                        'AI-powered suggestions',
                        'Premium templates',
                        'Advanced analytics',
                        'Priority support'
                    ]
                )
                db.session.add(premium_plan)
            
            db.session.commit()
        
        # Create sample user subscriptions
        sample_subscriptions = [
            {
                'user_id': sample_users[0]['user_id'],
                'plan_id': basic_plan.id,
                'status': SubscriptionStatus.ACTIVE,
                'billing_cycle': 'monthly',
                'start_date': datetime.utcnow() - timedelta(days=30),
                'scans_used_this_month': 3
            },
            {
                'user_id': sample_users[1]['user_id'],
                'plan_id': premium_plan.id,
                'status': SubscriptionStatus.ACTIVE,
                'billing_cycle': 'yearly',
                'start_date': datetime.utcnow() - timedelta(days=90),
                'end_date': datetime.utcnow() + timedelta(days=275),
                'next_billing_date': datetime.utcnow() + timedelta(days=275),
                'last_payment_amount': 99.99,
                'last_payment_date': datetime.utcnow() - timedelta(days=90),
                'scans_used_this_month': 25,
                'ai_suggestions_used': 15,
                'stripe_customer_id': 'cus_sample_customer_123',
                'stripe_subscription_id': 'sub_sample_subscription_456'
            }
        ]
        
        for sub_data in sample_subscriptions:
            subscription = UserSubscription(**sub_data)
            db.session.add(subscription)
            logger.info(f"Created subscription for user: {sub_data['user_id']}")
        
        # Create sample account activities
        sample_activities = [
            {
                'user_id': sample_users[0]['user_id'],
                'activity_type': ActivityType.LOGIN,
                'description': 'User logged in successfully',
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'success': True,
                'created_at': datetime.utcnow() - timedelta(hours=2)
            },
            {
                'user_id': sample_users[0]['user_id'],
                'activity_type': ActivityType.PROFILE_UPDATE,
                'description': 'Profile information updated',
                'details': {'updated_fields': ['bio', 'job_title']},
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'success': True,
                'created_at': datetime.utcnow() - timedelta(hours=1)
            },
            {
                'user_id': sample_users[1]['user_id'],
                'activity_type': ActivityType.PASSWORD_CHANGE,
                'description': 'Password changed successfully',
                'ip_address': '*********',
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'success': True,
                'created_at': datetime.utcnow() - timedelta(days=1)
            },
            {
                'user_id': sample_users[1]['user_id'],
                'activity_type': ActivityType.SUBSCRIPTION_CHANGE,
                'description': 'Upgraded to Premium subscription',
                'details': {'old_plan': 'Basic', 'new_plan': 'Premium'},
                'ip_address': '*********',
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'success': True,
                'created_at': datetime.utcnow() - timedelta(days=90)
            },
            {
                'user_id': sample_users[2]['user_id'],
                'activity_type': ActivityType.LOGIN,
                'description': 'Admin user logged in',
                'ip_address': '***********',
                'user_agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
                'success': True,
                'created_at': datetime.utcnow() - timedelta(minutes=30)
            }
        ]
        
        for activity_data in sample_activities:
            activity = AccountActivity(**activity_data)
            db.session.add(activity)
        
        db.session.commit()
        logger.info("Sample account data created successfully")
        
        # Print summary
        logger.info("\n" + "="*50)
        logger.info("SAMPLE DATA SUMMARY")
        logger.info("="*50)
        logger.info(f"Created {len(sample_profiles)} user profiles")
        logger.info(f"Created {len(sample_preferences)} user preferences")
        logger.info(f"Created {len(sample_subscriptions)} user subscriptions")
        logger.info(f"Created {len(sample_activities)} account activities")
        logger.info("\nSample Users:")
        for user in sample_users:
            logger.info(f"  - {user['email']} ({user['role']}) - ID: {user['user_id']}")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"Error creating sample data: {str(e)}")
        db.session.rollback()
        raise


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Initialize US-10 Account Settings database')
    parser.add_argument('--sample-data', action='store_true',
                       help='Create sample account data for testing')
    parser.add_argument('--reset', action='store_true',
                       help='Drop and recreate all tables (WARNING: destroys data)')
    
    args = parser.parse_args()
    
    # Create Flask app and initialize database
    app = create_app()
    
    with app.app_context():
        try:
            # Initialize database
            init_database(reset=args.reset)
            
            # Create sample data if requested
            if args.sample_data:
                create_sample_data()
            
            logger.info("Database initialization completed successfully!")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            sys.exit(1)


if __name__ == '__main__':
    main()
