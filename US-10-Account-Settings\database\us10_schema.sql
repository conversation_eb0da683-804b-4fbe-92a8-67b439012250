-- US-10: Account Settings Database Schema
-- =======================================
-- 
-- This file contains the PostgreSQL database schema for the account settings feature
-- in the Dr. Resume application. It includes tables for user profiles, preferences,
-- subscriptions, and account activity tracking.
-- 
-- Author: Dr. Resume Development Team
-- Date: 2025-07-23
-- Version: 1.0.0

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for enums
CREATE TYPE subscription_status AS ENUM (
    'ACTIVE',
    'INACTIVE',
    'CANCELLED',
    'EXPIRED',
    'TRIAL'
);

CREATE TYPE activity_type AS ENUM (
    'LOGIN',
    'LOGOUT',
    'PASSWORD_CHANGE',
    'EMAIL_CHANGE',
    'PROFILE_UPDATE',
    'SUBSCRIPTION_CHANGE',
    'ACCOUNT_DELETION',
    'SECURITY_SETTING_CHANGE'
);

-- User Profiles Table
-- Extended user profile information beyond basic authentication
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE, -- References users table from US-01
    
    -- Personal Information
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    display_name VARCHAR(100),
    bio TEXT,
    
    -- Contact Information
    phone_number VARCHAR(20),
    country CHAR(2), -- ISO country code
    timezone VARCHAR(50),
    language VARCHAR(5) DEFAULT 'en', -- ISO language code
    
    -- Professional Information
    job_title VARCHAR(100),
    company VARCHAR(100),
    industry VARCHAR(50),
    experience_years INTEGER CHECK (experience_years >= 0 AND experience_years <= 50),
    
    -- Profile Picture
    profile_picture_url VARCHAR(500),
    profile_picture_filename VARCHAR(255),
    
    -- Social Links
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    portfolio_url VARCHAR(500),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_profiles_user_id (user_id),
    INDEX idx_user_profiles_display_name (display_name),
    INDEX idx_user_profiles_company (company),
    INDEX idx_user_profiles_industry (industry)
);

-- User Preferences Table
-- User preferences and application settings
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE, -- References users table from US-01
    
    -- Notification Preferences
    email_notifications BOOLEAN DEFAULT TRUE,
    marketing_emails BOOLEAN DEFAULT FALSE,
    security_alerts BOOLEAN DEFAULT TRUE,
    scan_completion_notifications BOOLEAN DEFAULT TRUE,
    weekly_reports BOOLEAN DEFAULT FALSE,
    
    -- Privacy Settings
    profile_visibility VARCHAR(20) DEFAULT 'private' CHECK (profile_visibility IN ('public', 'private', 'contacts')),
    data_sharing BOOLEAN DEFAULT FALSE,
    analytics_tracking BOOLEAN DEFAULT TRUE,
    
    -- Application Preferences
    theme VARCHAR(20) DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
    dashboard_layout VARCHAR(20) DEFAULT 'default',
    items_per_page INTEGER DEFAULT 10 CHECK (items_per_page BETWEEN 1 AND 100),
    auto_save BOOLEAN DEFAULT TRUE,
    
    -- AI and Processing Preferences
    ai_suggestions_enabled BOOLEAN DEFAULT TRUE,
    processing_quality VARCHAR(20) DEFAULT 'standard' CHECK (processing_quality IN ('fast', 'standard', 'detailed')),
    auto_keyword_extraction BOOLEAN DEFAULT TRUE,
    
    -- Security Preferences
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    session_timeout_minutes INTEGER DEFAULT 60 CHECK (session_timeout_minutes BETWEEN 5 AND 480),
    login_notifications BOOLEAN DEFAULT TRUE,
    
    -- Custom Preferences (JSON field for extensibility)
    custom_preferences JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_preferences_user_id (user_id),
    INDEX idx_user_preferences_theme (theme),
    INDEX idx_user_preferences_notifications (email_notifications, marketing_emails)
);

-- Subscription Plans Table
-- Available subscription plans and their features
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Plan Information
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    price_monthly DECIMAL(10,2),
    price_yearly DECIMAL(10,2),
    
    -- Plan Features
    max_scans_per_month INTEGER,
    ai_suggestions_included BOOLEAN DEFAULT FALSE,
    premium_templates BOOLEAN DEFAULT FALSE,
    priority_support BOOLEAN DEFAULT FALSE,
    advanced_analytics BOOLEAN DEFAULT FALSE,
    export_capabilities BOOLEAN DEFAULT FALSE,
    
    -- Plan Configuration
    features JSONB DEFAULT '[]', -- List of feature names
    limits JSONB DEFAULT '{}',   -- Various limits and quotas
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_subscription_plans_name (name),
    INDEX idx_subscription_plans_active (is_active),
    INDEX idx_subscription_plans_sort (sort_order)
);

-- User Subscriptions Table
-- User subscription details and billing information
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User and plan references
    user_id UUID NOT NULL, -- References users table from US-01
    plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    
    -- Subscription Details
    status subscription_status DEFAULT 'INACTIVE',
    billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
    
    -- Dates
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    trial_end_date TIMESTAMP WITH TIME ZONE,
    next_billing_date TIMESTAMP WITH TIME ZONE,
    
    -- Payment Information
    stripe_subscription_id VARCHAR(100),
    stripe_customer_id VARCHAR(100),
    last_payment_amount DECIMAL(10,2),
    last_payment_date TIMESTAMP WITH TIME ZONE,
    
    -- Usage Tracking
    scans_used_this_month INTEGER DEFAULT 0,
    ai_suggestions_used INTEGER DEFAULT 0,
    
    -- Cancellation
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason VARCHAR(200),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_subscriptions_user_id (user_id),
    INDEX idx_user_subscriptions_plan_id (plan_id),
    INDEX idx_user_subscriptions_status (status),
    INDEX idx_user_subscriptions_billing_date (next_billing_date),
    INDEX idx_user_subscriptions_stripe_customer (stripe_customer_id)
);

-- Account Activities Table
-- Comprehensive logging of account-related activities
CREATE TABLE account_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User reference
    user_id UUID NOT NULL, -- References users table from US-01
    
    -- Activity Information
    activity_type activity_type NOT NULL,
    description VARCHAR(200),
    details JSONB DEFAULT '{}',
    
    -- Request Information
    ip_address INET,
    user_agent TEXT,
    
    -- Success/Failure
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_account_activities_user_id (user_id),
    INDEX idx_account_activities_created_at (created_at),
    INDEX idx_account_activities_activity_type (activity_type),
    INDEX idx_account_activities_success (success),
    INDEX idx_account_activities_ip_address (ip_address)
);

-- Account Deletion Requests Table
-- Track account deletion requests and grace periods
CREATE TABLE account_deletion_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE, -- References users table from US-01
    
    -- Deletion Details
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    scheduled_deletion_at TIMESTAMP WITH TIME ZONE NOT NULL,
    deletion_reason TEXT,
    
    -- Status
    is_cancelled BOOLEAN DEFAULT FALSE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    
    -- Data Export
    data_export_requested BOOLEAN DEFAULT FALSE,
    data_export_completed BOOLEAN DEFAULT FALSE,
    data_export_url VARCHAR(500),
    
    -- Indexes for performance
    INDEX idx_account_deletion_user_id (user_id),
    INDEX idx_account_deletion_scheduled (scheduled_deletion_at),
    INDEX idx_account_deletion_cancelled (is_cancelled)
);

-- User Sessions Table
-- Track active user sessions for security
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users table from US-01
    
    -- Session Information
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    
    -- Session Status
    is_active BOOLEAN DEFAULT TRUE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_token (session_token),
    INDEX idx_user_sessions_active (is_active),
    INDEX idx_user_sessions_expires (expires_at)
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price_monthly, price_yearly, max_scans_per_month, ai_suggestions_included, premium_templates, priority_support, advanced_analytics, export_capabilities, features, sort_order) VALUES
('Basic', 'Essential resume scanning features', 0.00, 0.00, 5, FALSE, FALSE, FALSE, FALSE, FALSE, 
 '["Basic resume scanning", "Keyword matching", "Basic suggestions"]', 1),

('Premium', 'Advanced features with AI-powered suggestions', 9.99, 99.99, 50, TRUE, TRUE, TRUE, TRUE, TRUE,
 '["Unlimited resume scanning", "AI-powered suggestions", "Premium templates", "Advanced analytics", "Priority support", "Export capabilities"]', 2),

('Enterprise', 'Full-featured plan for organizations', 29.99, 299.99, -1, TRUE, TRUE, TRUE, TRUE, TRUE,
 '["Unlimited everything", "Team management", "API access", "Custom integrations", "Dedicated support", "White-label options"]', 3);

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to relevant tables
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_plans_updated_at 
    BEFORE UPDATE ON subscription_plans 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at 
    BEFORE UPDATE ON user_subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_account_data()
RETURNS void AS $$
BEGIN
    -- Delete old account activities (keep 2 years)
    DELETE FROM account_activities 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '2 years';
    
    -- Delete expired user sessions
    DELETE FROM user_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- Process scheduled account deletions
    DELETE FROM user_profiles 
    WHERE user_id IN (
        SELECT user_id FROM account_deletion_requests 
        WHERE scheduled_deletion_at < CURRENT_TIMESTAMP 
        AND is_cancelled = FALSE
    );
    
    DELETE FROM user_preferences 
    WHERE user_id IN (
        SELECT user_id FROM account_deletion_requests 
        WHERE scheduled_deletion_at < CURRENT_TIMESTAMP 
        AND is_cancelled = FALSE
    );
    
    DELETE FROM user_subscriptions 
    WHERE user_id IN (
        SELECT user_id FROM account_deletion_requests 
        WHERE scheduled_deletion_at < CURRENT_TIMESTAMP 
        AND is_cancelled = FALSE
    );
    
    DELETE FROM account_activities 
    WHERE user_id IN (
        SELECT user_id FROM account_deletion_requests 
        WHERE scheduled_deletion_at < CURRENT_TIMESTAMP 
        AND is_cancelled = FALSE
    );
    
    -- Remove processed deletion requests
    DELETE FROM account_deletion_requests 
    WHERE scheduled_deletion_at < CURRENT_TIMESTAMP 
    AND is_cancelled = FALSE;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get user subscription status
CREATE OR REPLACE FUNCTION get_user_subscription_status(p_user_id UUID)
RETURNS TABLE (
    is_premium BOOLEAN,
    plan_name VARCHAR(50),
    status subscription_status,
    expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN us.status = 'ACTIVE' AND (us.end_date IS NULL OR us.end_date > CURRENT_TIMESTAMP) THEN TRUE
            WHEN us.status = 'TRIAL' AND us.trial_end_date > CURRENT_TIMESTAMP THEN TRUE
            ELSE FALSE
        END as is_premium,
        sp.name as plan_name,
        us.status,
        COALESCE(us.end_date, us.trial_end_date) as expires_at
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = p_user_id
    ORDER BY us.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Create composite indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_profiles_composite 
ON user_profiles (user_id, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_activities_composite 
ON account_activities (user_id, created_at DESC, activity_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscriptions_composite 
ON user_subscriptions (user_id, status, end_date);

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO dr_resume_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO dr_resume_user;
