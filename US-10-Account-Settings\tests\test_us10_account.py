"""
US-10: Account Settings Test Suite
==================================

Comprehensive test suite for the account settings feature in the Dr. Resume application.
Tests profile management, password changes, email updates, and preferences management.

Test Categories:
- Profile management tests
- Account update tests (email/password with re-authentication)
- Password change tests
- Preferences management tests
- Form validation tests
- Activity logging tests

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import pytest
import json
import uuid
import bcrypt
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Flask and testing imports
from flask import Flask
from flask_testing import TestCase
from flask_jwt_extended import create_access_token

# Local imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from us10_app import create_app
from us10_account_model import (
    db, UserProfile, UserPreferences, SubscriptionPlan, UserSubscription,
    AccountActivity, ActivityType, SubscriptionStatus
)
from us10_account_routes import (
    get_or_create_profile, get_or_create_preferences,
    verify_password, hash_password, log_account_activity
)


class AccountSettingsTestCase(TestCase):
    """Base test case for account settings tests"""
    
    def create_app(self):
        """Create test Flask application"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        app.config['PASSWORD_MIN_LENGTH'] = 8
        return app
    
    def setUp(self):
        """Set up test database and sample data"""
        db.create_all()
        self.create_sample_data()
    
    def tearDown(self):
        """Clean up test database"""
        db.session.remove()
        db.drop_all()
    
    def create_sample_data(self):
        """Create sample data for testing"""
        self.sample_user_id = str(uuid.uuid4())
        self.sample_password = 'TestPassword123!'
        self.sample_password_hash = hash_password(self.sample_password)
        
        # Create sample profile
        self.sample_profile = UserProfile(
            user_id=self.sample_user_id,
            first_name='John',
            last_name='Doe',
            display_name='John Doe',
            bio='Software developer',
            job_title='Senior Developer',
            company='TechCorp'
        )
        db.session.add(self.sample_profile)
        
        # Create sample preferences
        self.sample_preferences = UserPreferences(
            user_id=self.sample_user_id,
            email_notifications=True,
            marketing_emails=False,
            theme='light',
            items_per_page=20
        )
        db.session.add(self.sample_preferences)
        
        db.session.commit()
    
    def create_test_token(self, user_id=None):
        """Create test JWT token"""
        user_id = user_id or self.sample_user_id
        
        with self.app.app_context():
            token = create_access_token(identity=user_id)
        return token
    
    def get_auth_headers(self, token=None):
        """Get authentication headers for testing"""
        if token is None:
            token = self.create_test_token()
        
        return {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }


class TestProfileManagement(AccountSettingsTestCase):
    """Test profile management functionality"""
    
    def test_get_profile_success(self):
        """Test successful profile retrieval"""
        headers = self.get_auth_headers()
        
        response = self.client.get('/api/account/profile', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['first_name'], 'John')
        self.assertEqual(data['data']['last_name'], 'Doe')
        self.assertEqual(data['data']['job_title'], 'Senior Developer')
    
    def test_get_profile_creates_if_not_exists(self):
        """Test profile creation if it doesn't exist"""
        new_user_id = str(uuid.uuid4())
        token = self.create_test_token(new_user_id)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/account/profile', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['user_id'], new_user_id)
        
        # Verify profile was created in database
        profile = UserProfile.query.filter_by(user_id=new_user_id).first()
        self.assertIsNotNone(profile)
    
    def test_update_profile_success(self):
        """Test successful profile update"""
        headers = self.get_auth_headers()
        
        update_data = {
            'first_name': 'Jane',
            'last_name': 'Smith',
            'bio': 'Updated bio',
            'job_title': 'Lead Developer',
            'company': 'NewTech Inc.'
        }
        
        response = self.client.put('/api/account/profile', 
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['first_name'], 'Jane')
        self.assertEqual(data['data']['last_name'], 'Smith')
        self.assertEqual(data['data']['job_title'], 'Lead Developer')
        
        # Verify database was updated
        profile = UserProfile.query.filter_by(user_id=self.sample_user_id).first()
        self.assertEqual(profile.first_name, 'Jane')
        self.assertEqual(profile.last_name, 'Smith')
    
    def test_update_profile_validation_error(self):
        """Test profile update with validation errors"""
        headers = self.get_auth_headers()
        
        # Test with invalid data (too long)
        update_data = {
            'first_name': 'A' * 100,  # Too long
            'bio': 'A' * 2000  # Too long
        }
        
        response = self.client.put('/api/account/profile',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)
    
    def test_update_profile_unauthorized(self):
        """Test profile update without authentication"""
        update_data = {'first_name': 'Jane'}
        
        response = self.client.put('/api/account/profile',
                                 headers={'Content-Type': 'application/json'},
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 401)


class TestAccountUpdate(AccountSettingsTestCase):
    """Test account update functionality (email/password with re-authentication)"""
    
    def test_update_account_email_success(self):
        """Test successful email update with re-authentication"""
        headers = self.get_auth_headers()
        
        update_data = {
            'email': '<EMAIL>',
            'current_password': self.sample_password
        }
        
        response = self.client.put('/api/account/update_account',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('email', data['data']['updates_made'])
        self.assertTrue(data['data']['email_verification_sent'])
    
    def test_update_account_password_success(self):
        """Test successful password update with re-authentication"""
        headers = self.get_auth_headers()
        
        update_data = {
            'password': 'NewPassword123!',
            'current_password': self.sample_password
        }
        
        response = self.client.put('/api/account/update_account',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('password', data['data']['updates_made'])
        self.assertTrue(data['data']['requires_re_login'])
    
    def test_update_account_missing_current_password(self):
        """Test account update without current password"""
        headers = self.get_auth_headers()
        
        update_data = {
            'email': '<EMAIL>'
            # Missing current_password
        }
        
        response = self.client.put('/api/account/update_account',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)
    
    def test_update_account_invalid_email(self):
        """Test account update with invalid email format"""
        headers = self.get_auth_headers()
        
        update_data = {
            'email': 'invalid-email',
            'current_password': self.sample_password
        }
        
        response = self.client.put('/api/account/update_account',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)
    
    def test_update_account_weak_password(self):
        """Test account update with weak password"""
        headers = self.get_auth_headers()
        
        update_data = {
            'password': 'weak',  # Too weak
            'current_password': self.sample_password
        }
        
        response = self.client.put('/api/account/update_account',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)


class TestPasswordChange(AccountSettingsTestCase):
    """Test password change functionality"""
    
    def test_change_password_success(self):
        """Test successful password change"""
        headers = self.get_auth_headers()
        
        password_data = {
            'current_password': self.sample_password,
            'new_password': 'NewStrongPassword123!',
            'confirm_password': 'NewStrongPassword123!'
        }
        
        response = self.client.post('/api/account/change-password',
                                  headers=headers,
                                  data=json.dumps(password_data))
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertTrue(data['data']['requires_re_login'])
        self.assertIn('password_changed_at', data['data'])
    
    def test_change_password_mismatch(self):
        """Test password change with mismatched passwords"""
        headers = self.get_auth_headers()
        
        password_data = {
            'current_password': self.sample_password,
            'new_password': 'NewStrongPassword123!',
            'confirm_password': 'DifferentPassword123!'
        }
        
        response = self.client.post('/api/account/change-password',
                                  headers=headers,
                                  data=json.dumps(password_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)
    
    def test_change_password_weak_password(self):
        """Test password change with weak new password"""
        headers = self.get_auth_headers()
        
        password_data = {
            'current_password': self.sample_password,
            'new_password': 'weak',
            'confirm_password': 'weak'
        }
        
        response = self.client.post('/api/account/change-password',
                                  headers=headers,
                                  data=json.dumps(password_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)
    
    def test_change_password_missing_fields(self):
        """Test password change with missing required fields"""
        headers = self.get_auth_headers()
        
        password_data = {
            'new_password': 'NewStrongPassword123!'
            # Missing current_password and confirm_password
        }
        
        response = self.client.post('/api/account/change-password',
                                  headers=headers,
                                  data=json.dumps(password_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)


class TestPreferencesManagement(AccountSettingsTestCase):
    """Test preferences management functionality"""
    
    def test_get_preferences_success(self):
        """Test successful preferences retrieval"""
        headers = self.get_auth_headers()
        
        response = self.client.get('/api/account/preferences', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertTrue(data['data']['notifications']['email_notifications'])
        self.assertFalse(data['data']['notifications']['marketing_emails'])
        self.assertEqual(data['data']['application']['theme'], 'light')
    
    def test_get_preferences_creates_if_not_exists(self):
        """Test preferences creation if they don't exist"""
        new_user_id = str(uuid.uuid4())
        token = self.create_test_token(new_user_id)
        headers = self.get_auth_headers(token)
        
        response = self.client.get('/api/account/preferences', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verify preferences were created in database
        preferences = UserPreferences.query.filter_by(user_id=new_user_id).first()
        self.assertIsNotNone(preferences)
    
    def test_update_preferences_success(self):
        """Test successful preferences update"""
        headers = self.get_auth_headers()
        
        update_data = {
            'email_notifications': False,
            'marketing_emails': True,
            'theme': 'dark',
            'items_per_page': 50,
            'auto_save': False
        }
        
        response = self.client.put('/api/account/preferences',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertFalse(data['data']['notifications']['email_notifications'])
        self.assertTrue(data['data']['notifications']['marketing_emails'])
        self.assertEqual(data['data']['application']['theme'], 'dark')
        
        # Verify database was updated
        preferences = UserPreferences.query.filter_by(user_id=self.sample_user_id).first()
        self.assertFalse(preferences.email_notifications)
        self.assertTrue(preferences.marketing_emails)
        self.assertEqual(preferences.theme, 'dark')
    
    def test_update_preferences_validation_error(self):
        """Test preferences update with validation errors"""
        headers = self.get_auth_headers()
        
        update_data = {
            'theme': 'invalid_theme',  # Invalid theme
            'items_per_page': 1000  # Too high
        }
        
        response = self.client.put('/api/account/preferences',
                                 headers=headers,
                                 data=json.dumps(update_data))
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('errors', data)


class TestPasswordUtilities(AccountSettingsTestCase):
    """Test password utility functions"""
    
    def test_hash_password(self):
        """Test password hashing"""
        password = 'TestPassword123!'
        hashed = hash_password(password)
        
        self.assertIsInstance(hashed, str)
        self.assertNotEqual(password, hashed)
        self.assertTrue(len(hashed) > 50)  # Bcrypt hashes are long
    
    def test_verify_password_correct(self):
        """Test password verification with correct password"""
        password = 'TestPassword123!'
        hashed = hash_password(password)
        
        result = verify_password(hashed, password)
        self.assertTrue(result)
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password"""
        password = 'TestPassword123!'
        wrong_password = 'WrongPassword123!'
        hashed = hash_password(password)
        
        result = verify_password(hashed, wrong_password)
        self.assertFalse(result)
    
    def test_verify_password_invalid_hash(self):
        """Test password verification with invalid hash"""
        password = 'TestPassword123!'
        invalid_hash = 'invalid_hash'
        
        result = verify_password(invalid_hash, password)
        self.assertFalse(result)


class TestActivityLogging(AccountSettingsTestCase):
    """Test activity logging functionality"""
    
    def test_log_account_activity_success(self):
        """Test successful activity logging"""
        user_id = self.sample_user_id
        
        with self.app.test_request_context('/', headers={'User-Agent': 'test-agent'}):
            log_account_activity(
                user_id=user_id,
                activity_type=ActivityType.PROFILE_UPDATE,
                description='Profile updated',
                details={'field': 'name'},
                success=True
            )
        
        # Verify activity was logged
        activity = AccountActivity.query.filter_by(user_id=user_id).first()
        self.assertIsNotNone(activity)
        self.assertEqual(activity.activity_type, ActivityType.PROFILE_UPDATE)
        self.assertEqual(activity.description, 'Profile updated')
        self.assertTrue(activity.success)
    
    def test_log_account_activity_failure(self):
        """Test activity logging for failures"""
        user_id = self.sample_user_id
        
        with self.app.test_request_context('/', headers={'User-Agent': 'test-agent'}):
            log_account_activity(
                user_id=user_id,
                activity_type=ActivityType.PASSWORD_CHANGE,
                description='Password change failed',
                success=False,
                error_message='Invalid current password'
            )
        
        # Verify activity was logged
        activity = AccountActivity.query.filter_by(user_id=user_id).first()
        self.assertIsNotNone(activity)
        self.assertEqual(activity.activity_type, ActivityType.PASSWORD_CHANGE)
        self.assertFalse(activity.success)
        self.assertEqual(activity.error_message, 'Invalid current password')


class TestHelperFunctions(AccountSettingsTestCase):
    """Test helper functions"""
    
    def test_get_or_create_profile_existing(self):
        """Test getting existing profile"""
        profile = get_or_create_profile(self.sample_user_id)
        
        self.assertIsNotNone(profile)
        self.assertEqual(profile.user_id, self.sample_user_id)
        self.assertEqual(profile.first_name, 'John')
    
    def test_get_or_create_profile_new(self):
        """Test creating new profile"""
        new_user_id = str(uuid.uuid4())
        profile = get_or_create_profile(new_user_id)
        
        self.assertIsNotNone(profile)
        self.assertEqual(profile.user_id, new_user_id)
        
        # Verify it was saved to database
        saved_profile = UserProfile.query.filter_by(user_id=new_user_id).first()
        self.assertIsNotNone(saved_profile)
    
    def test_get_or_create_preferences_existing(self):
        """Test getting existing preferences"""
        preferences = get_or_create_preferences(self.sample_user_id)
        
        self.assertIsNotNone(preferences)
        self.assertEqual(preferences.user_id, self.sample_user_id)
        self.assertTrue(preferences.email_notifications)
    
    def test_get_or_create_preferences_new(self):
        """Test creating new preferences"""
        new_user_id = str(uuid.uuid4())
        preferences = get_or_create_preferences(new_user_id)
        
        self.assertIsNotNone(preferences)
        self.assertEqual(preferences.user_id, new_user_id)
        
        # Verify it was saved to database
        saved_preferences = UserPreferences.query.filter_by(user_id=new_user_id).first()
        self.assertIsNotNone(saved_preferences)


class TestHealthEndpoints(AccountSettingsTestCase):
    """Test health check endpoints"""
    
    def test_health_endpoint(self):
        """Test main health endpoint"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('version', data)
        self.assertIn('features', data)
    
    def test_api_health_endpoint(self):
        """Test API health endpoint"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('statistics', data)
        self.assertIn('endpoints', data)


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
