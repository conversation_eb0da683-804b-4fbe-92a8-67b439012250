# Dr. Resume - Integrated Backend Environment Configuration
# ========================================================
# 
# Copy this file to .env and update the values for your environment.
# This file contains all environment variables needed for the integrated backend.

# Application Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# Database Configuration
# For SQLite (Development)
DATABASE_URL=sqlite:///dr_resume_integrated.db

# For PostgreSQL (Production)
# DATABASE_URL=postgresql://username:password@localhost:5432/dr_resume_integrated
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=dr_resume_integrated
# DB_USER=postgres
# DB_PASSWORD=your_password_here

# OpenAI Configuration (for Premium Features)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8000,http://127.0.0.1:8000

# Rate Limiting Configuration
RATELIMIT_STORAGE_URL=memory://
RATELIMIT_DEFAULT=100 per hour

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/dr_resume.log

# Stripe Configuration (for payments)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Sentry Configuration (for error tracking)
SENTRY_DSN=your-sentry-dsn-here

# Feature Flags
ENABLE_PREMIUM_FEATURES=True
ENABLE_AI_SUGGESTIONS=True
ENABLE_ANALYTICS=True
ENABLE_EMAIL_NOTIFICATIONS=True

# Development Settings
DEBUG_MODE=True
TESTING_MODE=False
SAMPLE_DATA=True
