"""
Dr. Resume - Integrated Backend Application
==========================================

This is the main integrated Flask application that combines all US-01 to US-10 features
into a single cohesive backend with SQLite support and PostgreSQL migration capability.

Features Integrated:
- US-01: User Registration & Authentication
- US-02: JWT Token Management
- US-03: Resume Upload & Processing
- US-04: Job Description Management
- US-05: Keyword Extraction & NLP
- US-06: Matching Score Calculation
- US-07: Suggestions (Basic + Premium AI)
- US-08: Dashboard & Analytics
- US-09: API Protection & Security
- US-10: Account Settings & Management

Tech Stack: Flask, SQLAlchemy, JWT, SQLite/PostgreSQL, OpenAI API
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_mail import Mail
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize extensions
db = SQLAlchemy()
jwt = JWTManager()
mail = Mail()
limiter = Limiter(key_func=get_remote_address)

def create_app(config_name='development'):
    """
    Application factory pattern for creating integrated Flask app
    
    Args:
        config_name (str): Configuration environment ('development', 'testing', 'production')
        
    Returns:
        Flask: Configured Flask application with all US features
    """
    
    # Create Flask application
    app = Flask(__name__)
    
    # Configure CORS for frontend integration
    CORS(app, origins=[
        'http://localhost:3000', 
        'http://127.0.0.1:3000',
        'http://localhost:8000',
        'http://127.0.0.1:8000'
    ])
    
    # Database Configuration - SQLite with PostgreSQL migration support
    if config_name == 'testing':
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_dr_resume_integrated.db'
    elif config_name == 'production':
        # PostgreSQL for production
        DB_HOST = os.getenv('DB_HOST', 'localhost')
        DB_PORT = os.getenv('DB_PORT', '5432')
        DB_NAME = os.getenv('DB_NAME', 'dr_resume_integrated')
        DB_USER = os.getenv('DB_USER', 'postgres')
        DB_PASSWORD = os.getenv('DB_PASSWORD', 'your_password_here')
        app.config['SQLALCHEMY_DATABASE_URI'] = f'postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
    else:
        # SQLite for development (easy setup)
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///dr_resume_integrated.db'
    
    # SQLAlchemy Configuration
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = config_name == 'development'
    
    # Security Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dr-resume-integrated-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    
    # File Upload Configuration
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
    app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(__file__), 'uploads')
    
    # Email Configuration
    app.config['MAIL_SERVER'] = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    app.config['MAIL_PORT'] = int(os.getenv('MAIL_PORT', '587'))
    app.config['MAIL_USE_TLS'] = True
    app.config['MAIL_USERNAME'] = os.getenv('MAIL_USERNAME')
    app.config['MAIL_PASSWORD'] = os.getenv('MAIL_PASSWORD')
    
    # OpenAI Configuration for Premium Features
    app.config['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY')
    
    # Rate Limiting Configuration
    app.config['RATELIMIT_STORAGE_URL'] = os.getenv('REDIS_URL', 'memory://')
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    mail.init_app(app)
    limiter.init_app(app)
    
    # Initialize database with models
    from models.user_models import User, UserProfile, UserPreferences, AccountActivity
    from models.content_models import Resume, JobDescription
    from models.analysis_models import Keyword, MatchingScore
    from models.suggestion_models import Suggestion, PremiumSuggestion
    from models.dashboard_models import ScanHistory, DashboardAnalytics, UserActivity
    from models.subscription_models import SubscriptionPlan, UserSubscription

    # Update db instances in all model files
    import models.user_models
    import models.content_models
    import models.analysis_models
    import models.suggestion_models
    import models.dashboard_models
    import models.subscription_models

    models.user_models.db = db
    models.content_models.db = db
    models.analysis_models.db = db
    models.suggestion_models.db = db
    models.dashboard_models.db = db
    models.subscription_models.db = db
    
    # Import and register all blueprints
    from routes.auth_routes import auth_bp
    from routes.resume_routes import resume_bp
    from routes.jd_routes import jd_bp
    from routes.keyword_routes import keyword_bp
    from routes.matching_routes import matching_bp
    from routes.suggestion_routes import suggestion_bp
    from routes.dashboard_routes import dashboard_bp
    from routes.security_routes import security_bp
    from routes.account_routes import account_bp
    
    # Register blueprints with API prefix
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(resume_bp, url_prefix='/api/resumes')
    app.register_blueprint(jd_bp, url_prefix='/api/job-descriptions')
    app.register_blueprint(keyword_bp, url_prefix='/api/keywords')
    app.register_blueprint(matching_bp, url_prefix='/api/matching')
    app.register_blueprint(suggestion_bp, url_prefix='/api/suggestions')
    app.register_blueprint(dashboard_bp, url_prefix='/api/dashboard')
    app.register_blueprint(security_bp, url_prefix='/api/security')
    app.register_blueprint(account_bp, url_prefix='/api/account')
    
    # Create database tables and upload directories
    with app.app_context():
        try:
            # Create upload directories
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
            os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'resumes'), exist_ok=True)
            
            # Create database tables
            db.create_all()
            logger.info("✅ Database tables created successfully")
            
            # Initialize sample data if in development
            if config_name == 'development':
                from utils.sample_data import create_sample_data
                create_sample_data()
                
        except Exception as e:
            logger.error(f"❌ Error during initialization: {e}")
    
    # Root route with API information
    @app.route('/')
    def home():
        """
        Home endpoint - provides comprehensive API information
        """
        return jsonify({
            'success': True,
            'message': 'Dr. Resume - Integrated AI Resume Scanner API',
            'version': '2.0.0',
            'description': 'Complete resume analysis platform with AI-powered suggestions',
            'features': {
                'user_management': 'US-01 & US-10: Registration, authentication, account settings',
                'file_processing': 'US-03 & US-04: Resume and job description upload/processing',
                'ai_analysis': 'US-05 & US-06: Keyword extraction and matching algorithms',
                'suggestions': 'US-07: Basic and premium AI-powered recommendations',
                'dashboard': 'US-08: Analytics and scan history',
                'security': 'US-09: API protection and rate limiting'
            },
            'endpoints': {
                'authentication': '/api/auth/*',
                'resumes': '/api/resumes/*',
                'job_descriptions': '/api/job-descriptions/*',
                'keywords': '/api/keywords/*',
                'matching': '/api/matching/*',
                'suggestions': '/api/suggestions/*',
                'dashboard': '/api/dashboard/*',
                'security': '/api/security/*',
                'account': '/api/account/*'
            },
            'database': 'SQLite (development) / PostgreSQL (production)',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Health check endpoint
    @app.route('/health')
    def health():
        """
        Comprehensive health check for all integrated features
        """
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'connected'
            
            # Get basic statistics
            user_count = User.query.count()
            resume_count = Resume.query.count()
            jd_count = JobDescription.query.count()
            
        except Exception as e:
            db_status = f'error: {str(e)}'
            user_count = resume_count = jd_count = 0
        
        return jsonify({
            'success': True,
            'message': 'Dr. Resume Integrated API is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'features': 'all_integrated'
            },
            'statistics': {
                'total_users': user_count,
                'total_resumes': resume_count,
                'total_job_descriptions': jd_count
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'Bad Request'
        }), 400
    
    return app

def run_development_server():
    """
    Run the integrated Flask development server
    """
    app = create_app('development')
    
    print("🚀 Starting Dr. Resume Integrated API Server...")
    print("📋 Features: All US-01 to US-10 integrated")
    print("🌐 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000")
    print("🔍 Health Check: http://localhost:8000/health")
    print("\n📋 Available API Endpoints:")
    print("   Authentication: /api/auth/*")
    print("   Resumes: /api/resumes/*")
    print("   Job Descriptions: /api/job-descriptions/*")
    print("   Keywords: /api/keywords/*")
    print("   Matching: /api/matching/*")
    print("   Suggestions: /api/suggestions/*")
    print("   Dashboard: /api/dashboard/*")
    print("   Security: /api/security/*")
    print("   Account: /api/account/*")
    print("\n💾 Database: SQLite (dr_resume_integrated.db)")
    print("🔧 Configuration: Check .env file for API keys and settings")
    print("📖 Documentation: See integrated_README.md for complete guide\n")
    
    app.run(
        host='0.0.0.0',
        port=8000,
        debug=True,
        use_reloader=True
    )

if __name__ == '__main__':
    run_development_server()
