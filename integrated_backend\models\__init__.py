"""
Dr. Resume - Integrated Database Models
======================================

This module contains all database models from US-01 to US-10 integrated into a single
cohesive schema. All models are designed to work together with proper relationships
and foreign key constraints.

Models included:
- User (US-01): Core user authentication and profile
- Resume (US-03): Resume file storage and metadata
- JobDescription (US-04): Job description management
- Keyword (US-05): Extracted keywords from documents
- MatchingScore (US-06): Resume-JD matching results
- Suggestion (US-07): Basic improvement suggestions
- PremiumSuggestion (US-07): AI-powered premium suggestions
- ScanHistory (US-08): Dashboard scan tracking
- DashboardAnalytics (US-08): User analytics data
- UserActivity (US-08): User activity tracking
- UserProfile (US-10): Extended user profile information
- UserPreferences (US-10): User application preferences
- SubscriptionPlan (US-10): Premium subscription plans
- UserSubscription (US-10): User subscription tracking
- AccountActivity (US-10): Account activity logging
"""

from .user_models import User, UserProfile, UserPreferences, AccountActivity
from .content_models import Resume, JobDescription
from .analysis_models import Keyword, MatchingScore
from .suggestion_models import Suggestion, PremiumSuggestion
from .dashboard_models import ScanHistory, DashboardAnalytics, UserActivity
from .subscription_models import SubscriptionPlan, UserSubscription

# Export all models for easy importing
__all__ = [
    'User',
    'UserProfile', 
    'UserPreferences',
    'AccountActivity',
    'Resume',
    'JobDescription',
    'Keyword',
    'MatchingScore',
    'Suggestion',
    'PremiumSuggestion',
    'ScanHistory',
    'DashboardAnalytics',
    'UserActivity',
    'SubscriptionPlan',
    'UserSubscription'
]
