"""
Dr. Resume - Analysis Models (US-05 & US-06)
============================================

This module contains analysis-related database models including:
- Keyword: Extracted keywords from documents (US-05)
- MatchingScore: Resume-JD matching results (US-06)
"""

import uuid
from datetime import datetime
from decimal import Decimal
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Numeric, JSON
from flask_sqlalchemy import SQLAlchemy

# Database instance
db = SQLAlchemy()

class Keyword(db.Model):
    """
    Keyword Model (US-05)
    
    Stores extracted keywords from resumes and job descriptions using NLP processing.
    Includes keyword metadata, confidence scores, and context information.
    """
    
    __tablename__ = 'keywords'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), Foreign<PERSON>ey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Source document references (one of these will be set)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=True)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=True)
    
    # Keyword information
    keyword = Column(String(100), nullable=False, index=True)
    keyword_type = Column(String(50), nullable=False, index=True)  # skill, technology, experience, education, etc.
    frequency = Column(Integer, default=1, nullable=False)
    confidence_score = Column(Numeric(3, 2), default=Decimal('0.80'), nullable=False)
    
    # Context and metadata
    context_snippet = Column(Text)  # Surrounding text where keyword was found
    position_in_document = Column(Integer)  # Character position in original document
    
    # Processing metadata
    extraction_method = Column(String(50), default='spacy', nullable=False)  # spacy, nltk, custom
    extraction_version = Column(String(20), default='1.0', nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)  # Manual verification flag
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, keyword, keyword_type, frequency=1, confidence_score=0.80,
                 resume_id=None, job_description_id=None, context_snippet=None,
                 position_in_document=None, extraction_method='spacy'):
        self.user_id = user_id
        self.keyword = keyword.lower().strip()
        self.keyword_type = keyword_type
        self.frequency = frequency
        self.confidence_score = Decimal(str(confidence_score))
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.context_snippet = context_snippet
        self.position_in_document = position_in_document
        self.extraction_method = extraction_method
    
    def get_source_type(self):
        """Get the source document type"""
        if self.resume_id:
            return 'resume'
        elif self.job_description_id:
            return 'job_description'
        return 'unknown'
    
    def to_dict(self):
        """Convert keyword to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'keyword': self.keyword,
            'keyword_type': self.keyword_type,
            'frequency': self.frequency,
            'confidence_score': float(self.confidence_score),
            'context_snippet': self.context_snippet,
            'position_in_document': self.position_in_document,
            'extraction_method': self.extraction_method,
            'extraction_version': self.extraction_version,
            'is_verified': self.is_verified,
            'source_type': self.get_source_type(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def create_keyword(cls, user_id, keyword, keyword_type, **kwargs):
        """Create a new keyword record"""
        keyword_obj = cls(user_id=user_id, keyword=keyword, keyword_type=keyword_type, **kwargs)
        db.session.add(keyword_obj)
        db.session.commit()
        return keyword_obj
    
    @classmethod
    def get_document_keywords(cls, user_id, resume_id=None, job_description_id=None, keyword_type=None):
        """Get keywords for a specific document"""
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        if keyword_type:
            query = query.filter_by(keyword_type=keyword_type)
        
        return query.order_by(cls.confidence_score.desc(), cls.frequency.desc()).all()
    
    @classmethod
    def get_keyword_summary(cls, user_id, resume_id=None, job_description_id=None):
        """Get keyword summary statistics"""
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        
        keywords = query.all()
        
        if not keywords:
            return {
                'total_keywords': 0,
                'unique_keywords': 0,
                'keyword_types': 0,
                'avg_confidence': 0.0,
                'top_keywords': []
            }
        
        unique_keywords = set(k.keyword for k in keywords)
        keyword_types = set(k.keyword_type for k in keywords)
        avg_confidence = sum(float(k.confidence_score) for k in keywords) / len(keywords)
        
        # Get top keywords by frequency and confidence
        keyword_freq = {}
        for k in keywords:
            if k.keyword not in keyword_freq:
                keyword_freq[k.keyword] = {'frequency': 0, 'confidence': 0.0, 'type': k.keyword_type}
            keyword_freq[k.keyword]['frequency'] += k.frequency
            keyword_freq[k.keyword]['confidence'] = max(keyword_freq[k.keyword]['confidence'], float(k.confidence_score))
        
        top_keywords = sorted(
            keyword_freq.items(),
            key=lambda x: (x[1]['frequency'], x[1]['confidence']),
            reverse=True
        )[:10]
        
        return {
            'total_keywords': len(keywords),
            'unique_keywords': len(unique_keywords),
            'keyword_types': len(keyword_types),
            'avg_confidence': round(avg_confidence, 2),
            'top_keywords': [{'keyword': k, 'frequency': v['frequency'], 'confidence': v['confidence'], 'type': v['type']} for k, v in top_keywords]
        }
    
    def __repr__(self):
        return f'<Keyword {self.keyword} ({self.keyword_type})>'


class MatchingScore(db.Model):
    """
    Matching Score Model (US-06)
    
    Stores matching scores between resumes and job descriptions using various algorithms.
    Includes detailed breakdown by category and keyword analysis.
    """
    
    __tablename__ = 'matching_scores'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Document references (resume vs job description comparison)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)
    
    # Matching scores and metrics
    overall_match_percentage = Column(Numeric(5, 2), nullable=False)
    jaccard_similarity = Column(Numeric(5, 4), default=Decimal('0.0000'), nullable=False)
    keyword_overlap_count = Column(Integer, default=0, nullable=False)
    resume_keyword_count = Column(Integer, default=0, nullable=False)
    jd_keyword_count = Column(Integer, default=0, nullable=False)
    
    # Category-specific matching scores
    skill_match_percentage = Column(Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    experience_match_percentage = Column(Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    education_match_percentage = Column(Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    technology_match_percentage = Column(Numeric(5, 2), default=Decimal('0.00'), nullable=False)
    
    # Keyword analysis (JSON fields for flexibility)
    matched_keywords = Column(JSON, default=list, nullable=False)
    missing_keywords = Column(JSON, default=list, nullable=False)
    extra_keywords = Column(JSON, default=list, nullable=False)
    
    # Algorithm metadata
    algorithm_version = Column(String(20), default='1.0', nullable=False)
    calculation_method = Column(String(50), default='jaccard', nullable=False)
    confidence_score = Column(Numeric(3, 2), default=Decimal('0.80'), nullable=False)
    
    # Processing metadata
    processing_time_ms = Column(Integer)
    calculation_details = Column(JSON, default=dict, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, resume_id, job_description_id, overall_match_percentage,
                 jaccard_similarity=0.0000, keyword_overlap_count=0, resume_keyword_count=0,
                 jd_keyword_count=0, skill_match_percentage=0.00, experience_match_percentage=0.00,
                 education_match_percentage=0.00, technology_match_percentage=0.00,
                 matched_keywords=None, missing_keywords=None, extra_keywords=None,
                 algorithm_version='1.0', calculation_method='jaccard', confidence_score=0.80,
                 processing_time_ms=None):
        self.user_id = user_id
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.overall_match_percentage = Decimal(str(overall_match_percentage))
        self.jaccard_similarity = Decimal(str(jaccard_similarity))
        self.keyword_overlap_count = keyword_overlap_count
        self.resume_keyword_count = resume_keyword_count
        self.jd_keyword_count = jd_keyword_count
        self.skill_match_percentage = Decimal(str(skill_match_percentage))
        self.experience_match_percentage = Decimal(str(experience_match_percentage))
        self.education_match_percentage = Decimal(str(education_match_percentage))
        self.technology_match_percentage = Decimal(str(technology_match_percentage))
        self.matched_keywords = matched_keywords or []
        self.missing_keywords = missing_keywords or []
        self.extra_keywords = extra_keywords or []
        self.algorithm_version = algorithm_version
        self.calculation_method = calculation_method
        self.confidence_score = Decimal(str(confidence_score))
        self.processing_time_ms = processing_time_ms
    
    def get_match_grade(self):
        """Get letter grade based on overall match percentage"""
        score = float(self.overall_match_percentage)
        if score >= 90:
            return 'A+'
        elif score >= 85:
            return 'A'
        elif score >= 80:
            return 'A-'
        elif score >= 75:
            return 'B+'
        elif score >= 70:
            return 'B'
        elif score >= 65:
            return 'B-'
        elif score >= 60:
            return 'C+'
        elif score >= 55:
            return 'C'
        elif score >= 50:
            return 'C-'
        else:
            return 'D'
    
    def get_match_status(self):
        """Get match status description"""
        score = float(self.overall_match_percentage)
        if score >= 80:
            return 'Excellent Match'
        elif score >= 70:
            return 'Good Match'
        elif score >= 60:
            return 'Fair Match'
        elif score >= 50:
            return 'Poor Match'
        else:
            return 'Very Poor Match'
    
    def to_dict(self):
        """Convert matching score to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'overall_match_percentage': float(self.overall_match_percentage),
            'jaccard_similarity': float(self.jaccard_similarity),
            'keyword_overlap_count': self.keyword_overlap_count,
            'resume_keyword_count': self.resume_keyword_count,
            'jd_keyword_count': self.jd_keyword_count,
            'skill_match_percentage': float(self.skill_match_percentage),
            'experience_match_percentage': float(self.experience_match_percentage),
            'education_match_percentage': float(self.education_match_percentage),
            'technology_match_percentage': float(self.technology_match_percentage),
            'matched_keywords': self.matched_keywords,
            'missing_keywords': self.missing_keywords,
            'extra_keywords': self.extra_keywords,
            'match_grade': self.get_match_grade(),
            'match_status': self.get_match_status(),
            'algorithm_version': self.algorithm_version,
            'calculation_method': self.calculation_method,
            'confidence_score': float(self.confidence_score),
            'processing_time_ms': self.processing_time_ms,
            'calculation_details': self.calculation_details,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_matching_score(cls, user_id, resume_id, job_description_id, overall_match_percentage, **kwargs):
        """Create a new matching score record"""
        score = cls(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            overall_match_percentage=overall_match_percentage,
            **kwargs
        )
        db.session.add(score)
        db.session.commit()
        return score
    
    @classmethod
    def get_user_matching_scores(cls, user_id, resume_id=None, job_description_id=None):
        """Get matching scores for a user"""
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        
        return query.order_by(cls.created_at.desc()).all()
    
    def __repr__(self):
        return f'<MatchingScore {self.id}: {self.overall_match_percentage}%>'
