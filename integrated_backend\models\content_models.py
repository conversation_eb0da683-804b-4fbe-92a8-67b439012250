"""
Dr. Resume - Content Models (US-03 & US-04)
===========================================

This module contains content-related database models including:
- Resume: Resume file storage and metadata (US-03)
- JobDescription: Job description management (US-04)
"""

import uuid
import os
from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, BigInteger
from flask_sqlalchemy import SQLAlchemy

# Database instance
db = SQLAlchemy()

class Resume(db.Model):
    """
    Resume Model (US-03)
    
    Stores uploaded resume files metadata and file paths with local file system storage.
    Includes file processing status and extracted content.
    """
    
    __tablename__ = 'resumes'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # File information
    original_filename = Column(String(255), nullable=False)
    stored_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    file_type = Column(String(100), nullable=False)
    file_extension = Column(String(10), nullable=False)
    
    # Parsed content (extracted using local script)
    extracted_text = Column(Text)
    extraction_status = Column(String(20), default='pending', nullable=False)  # pending, success, failed
    extraction_error = Column(Text)
    
    # Resume metadata
    resume_title = Column(String(200))
    resume_description = Column(Text)
    
    # Upload tracking
    upload_status = Column(String(20), default='uploaded', nullable=False)  # uploaded, processing, processed
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Processing metadata
    processing_start_time = Column(DateTime)
    processing_end_time = Column(DateTime)
    processing_duration_seconds = Column(Integer)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    keywords = db.relationship('Keyword', backref='resume', lazy=True, cascade='all, delete-orphan')
    matching_scores = db.relationship('MatchingScore', backref='resume', lazy=True, cascade='all, delete-orphan')
    suggestions = db.relationship('Suggestion', backref='resume', lazy=True, cascade='all, delete-orphan')
    premium_suggestions = db.relationship('PremiumSuggestion', backref='resume', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, user_id, original_filename, stored_filename, file_path,
                 file_size, file_type, file_extension, resume_title=None,
                 resume_description=None):
        self.user_id = user_id
        self.original_filename = original_filename
        self.stored_filename = stored_filename
        self.file_path = file_path
        self.file_size = file_size
        self.file_type = file_type
        self.file_extension = file_extension
        self.resume_title = resume_title
        self.resume_description = resume_description
    
    def get_file_size_formatted(self):
        """Get human-readable file size"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def is_processed(self):
        """Check if resume has been fully processed"""
        return self.extraction_status == 'success' and self.upload_status == 'processed'
    
    def get_processing_duration(self):
        """Get processing duration in seconds"""
        if self.processing_start_time and self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds()
        return None
    
    def to_dict(self, include_content=False):
        """Convert resume to dictionary for JSON responses"""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_size_formatted': self.get_file_size_formatted(),
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'resume_title': self.resume_title,
            'resume_description': self.resume_description,
            'extraction_status': self.extraction_status,
            'upload_status': self.upload_status,
            'is_active': self.is_active,
            'is_processed': self.is_processed(),
            'processing_duration_seconds': self.processing_duration_seconds,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_content:
            data['extracted_text'] = self.extracted_text
            data['extraction_error'] = self.extraction_error
        
        return data
    
    @classmethod
    def create_resume(cls, user_id, original_filename, stored_filename, file_path,
                     file_size, file_type, file_extension, resume_title=None,
                     resume_description=None):
        """Create a new resume record"""
        resume = cls(
            user_id=user_id,
            original_filename=original_filename,
            stored_filename=stored_filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_type,
            file_extension=file_extension,
            resume_title=resume_title,
            resume_description=resume_description
        )
        db.session.add(resume)
        db.session.commit()
        return resume
    
    @classmethod
    def get_user_resumes(cls, user_id, active_only=True):
        """Get all resumes for a user"""
        query = cls.query.filter_by(user_id=user_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.order_by(cls.created_at.desc()).all()
    
    def delete_file(self):
        """Delete the physical file from storage"""
        try:
            if os.path.exists(self.file_path):
                os.remove(self.file_path)
                return True
        except Exception as e:
            print(f"Error deleting file {self.file_path}: {e}")
        return False
    
    def __repr__(self):
        return f'<Resume {self.id}: {self.original_filename}>'


class JobDescription(db.Model):
    """
    Job Description Model (US-04)
    
    Stores job description information for matching against resumes.
    Includes job details and processing status for keyword extraction.
    """
    
    __tablename__ = 'job_descriptions'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Job description content
    title = Column(String(200), nullable=False)
    company_name = Column(String(100), nullable=True)
    job_description_text = Column(Text, nullable=False)
    
    # Job details
    location = Column(String(100), nullable=True)
    employment_type = Column(String(50), default='full-time', nullable=True)
    experience_level = Column(String(50), nullable=True)
    salary_range = Column(String(100), nullable=True)
    
    # Processing status (for keyword extraction)
    is_processed = Column(Boolean, default=False, nullable=False)
    keywords_extracted = Column(Boolean, default=False, nullable=False)
    processing_status = Column(String(20), default='pending', nullable=False)  # pending, processing, completed, failed
    processing_error = Column(Text)
    
    # Metadata
    original_source = Column(String(100), default='manual_input', nullable=True)
    job_url = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Processing metadata
    processing_start_time = Column(DateTime)
    processing_end_time = Column(DateTime)
    processing_duration_seconds = Column(Integer)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    keywords = db.relationship('Keyword', backref='job_description', lazy=True, cascade='all, delete-orphan')
    matching_scores = db.relationship('MatchingScore', backref='job_description', lazy=True, cascade='all, delete-orphan')
    suggestions = db.relationship('Suggestion', backref='job_description', lazy=True, cascade='all, delete-orphan')
    premium_suggestions = db.relationship('PremiumSuggestion', backref='job_description', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, user_id, title, job_description_text, company_name=None,
                 location=None, employment_type='full-time', experience_level=None,
                 salary_range=None, original_source='manual_input', job_url=None):
        self.user_id = user_id
        self.title = title
        self.job_description_text = job_description_text
        self.company_name = company_name
        self.location = location
        self.employment_type = employment_type
        self.experience_level = experience_level
        self.salary_range = salary_range
        self.original_source = original_source
        self.job_url = job_url
    
    def get_word_count(self):
        """Get word count of job description"""
        return len(self.job_description_text.split()) if self.job_description_text else 0
    
    def get_processing_duration(self):
        """Get processing duration in seconds"""
        if self.processing_start_time and self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds()
        return None
    
    def to_dict(self, include_content=False):
        """Convert job description to dictionary for JSON responses"""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'company_name': self.company_name,
            'location': self.location,
            'employment_type': self.employment_type,
            'experience_level': self.experience_level,
            'salary_range': self.salary_range,
            'word_count': self.get_word_count(),
            'is_processed': self.is_processed,
            'keywords_extracted': self.keywords_extracted,
            'processing_status': self.processing_status,
            'is_active': self.is_active,
            'original_source': self.original_source,
            'processing_duration_seconds': self.processing_duration_seconds,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_content:
            data['job_description_text'] = self.job_description_text
            data['job_url'] = self.job_url
            data['processing_error'] = self.processing_error
        
        return data
    
    @classmethod
    def create_job_description(cls, user_id, title, job_description_text, **kwargs):
        """Create a new job description record"""
        jd = cls(
            user_id=user_id,
            title=title,
            job_description_text=job_description_text,
            **kwargs
        )
        db.session.add(jd)
        db.session.commit()
        return jd
    
    @classmethod
    def get_user_job_descriptions(cls, user_id, active_only=True):
        """Get all job descriptions for a user"""
        query = cls.query.filter_by(user_id=user_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.order_by(cls.created_at.desc()).all()
    
    def __repr__(self):
        return f'<JobDescription {self.id}: {self.title}>'
