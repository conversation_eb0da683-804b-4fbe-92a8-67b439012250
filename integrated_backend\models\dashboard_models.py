"""
Dr. Resume - Dashboard Models (US-08)
=====================================

This module contains dashboard-related database models including:
- ScanHistory: Track resume scanning history
- DashboardAnalytics: User analytics and metrics
- UserActivity: User activity tracking
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Float, JSON, BigInteger
from flask_sqlalchemy import SQLAlchemy

# Database instance will be injected by app
db = None

class ScanHistory(db.Model):
    """
    Scan History Model (US-08)
    
    Tracks complete resume scanning sessions including all processing steps,
    results, and user interactions for dashboard display.
    """
    
    __tablename__ = 'scan_history'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)
    matching_score_id = Column(String(36), ForeignKey('matching_scores.id', ondelete='CASCADE'), nullable=True)
    
    # Scan metadata
    scan_title = Column(String(200), nullable=False)
    scan_description = Column(Text)
    scan_status = Column(String(20), default='completed', nullable=False)  # pending, processing, completed, failed
    
    # Results summary
    overall_match_percentage = Column(Float, default=0.0)
    total_keywords_found = Column(Integer, default=0)
    missing_keywords_count = Column(Integer, default=0)
    suggestions_generated = Column(Integer, default=0)
    
    # Suggestions summary
    total_suggestions = Column(Integer, default=0)
    high_priority_suggestions = Column(Integer, default=0)
    implemented_suggestions = Column(Integer, default=0)
    dismissed_suggestions = Column(Integer, default=0)
    
    # Premium features usage
    has_premium_suggestions = Column(Boolean, default=False)
    premium_cost = Column(Float, default=0.0)
    ai_tokens_used = Column(Integer, default=0)
    
    # Processing metrics
    processing_time_seconds = Column(Float, default=0.0)
    processing_start_time = Column(DateTime)
    processing_end_time = Column(DateTime)
    
    # User interaction tracking
    last_viewed_at = Column(DateTime)
    view_count = Column(Integer, default=0)
    is_bookmarked = Column(Boolean, default=False)
    user_rating = Column(Integer)  # 1-5 star rating
    user_notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, resume_id, job_description_id, scan_title, **kwargs):
        self.user_id = user_id
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.scan_title = scan_title
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_processing_duration(self):
        """Get processing duration in seconds"""
        if self.processing_start_time and self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds()
        return self.processing_time_seconds or 0.0
    
    def get_match_grade(self):
        """Get letter grade based on match percentage"""
        score = self.overall_match_percentage or 0
        if score >= 90:
            return 'A+'
        elif score >= 85:
            return 'A'
        elif score >= 80:
            return 'A-'
        elif score >= 75:
            return 'B+'
        elif score >= 70:
            return 'B'
        elif score >= 65:
            return 'B-'
        elif score >= 60:
            return 'C+'
        elif score >= 55:
            return 'C'
        elif score >= 50:
            return 'C-'
        else:
            return 'D'
    
    def get_suggestions_implementation_rate(self):
        """Get percentage of suggestions implemented"""
        if self.total_suggestions > 0:
            return (self.implemented_suggestions / self.total_suggestions) * 100
        return 0.0
    
    def to_dict(self):
        """Convert scan history to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'matching_score_id': self.matching_score_id,
            'scan_title': self.scan_title,
            'scan_description': self.scan_description,
            'scan_status': self.scan_status,
            'overall_match_percentage': self.overall_match_percentage,
            'match_grade': self.get_match_grade(),
            'total_keywords_found': self.total_keywords_found,
            'missing_keywords_count': self.missing_keywords_count,
            'suggestions_generated': self.suggestions_generated,
            'total_suggestions': self.total_suggestions,
            'high_priority_suggestions': self.high_priority_suggestions,
            'implemented_suggestions': self.implemented_suggestions,
            'dismissed_suggestions': self.dismissed_suggestions,
            'suggestions_implementation_rate': self.get_suggestions_implementation_rate(),
            'has_premium_suggestions': self.has_premium_suggestions,
            'premium_cost': self.premium_cost,
            'ai_tokens_used': self.ai_tokens_used,
            'processing_time_seconds': self.get_processing_duration(),
            'last_viewed_at': self.last_viewed_at.isoformat() if self.last_viewed_at else None,
            'view_count': self.view_count,
            'is_bookmarked': self.is_bookmarked,
            'user_rating': self.user_rating,
            'user_notes': self.user_notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_scan_history(cls, user_id, resume_id, job_description_id, scan_title, **kwargs):
        """Create a new scan history record"""
        scan = cls(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            scan_title=scan_title,
            **kwargs
        )
        db.session.add(scan)
        db.session.commit()
        return scan
    
    def __repr__(self):
        return f'<ScanHistory {self.id}: {self.scan_title}>'


class DashboardAnalytics(db.Model):
    """
    Dashboard Analytics Model (US-08)
    
    Stores aggregated analytics data for dashboard display including
    user statistics, trends, and performance metrics.
    """
    
    __tablename__ = 'dashboard_analytics'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Time period for analytics
    analytics_date = Column(DateTime, nullable=False)
    period_type = Column(String(20), default='daily', nullable=False)  # daily, weekly, monthly
    
    # Scan statistics
    total_scans = Column(Integer, default=0)
    successful_scans = Column(Integer, default=0)
    failed_scans = Column(Integer, default=0)
    average_match_score = Column(Float, default=0.0)
    highest_match_score = Column(Float, default=0.0)
    lowest_match_score = Column(Float, default=0.0)
    
    # Content statistics
    resumes_uploaded = Column(Integer, default=0)
    job_descriptions_created = Column(Integer, default=0)
    keywords_extracted = Column(Integer, default=0)
    unique_keywords = Column(Integer, default=0)
    
    # Suggestion statistics
    total_suggestions_generated = Column(Integer, default=0)
    suggestions_implemented = Column(Integer, default=0)
    suggestions_dismissed = Column(Integer, default=0)
    implementation_rate = Column(Float, default=0.0)
    
    # Premium usage statistics
    premium_scans = Column(Integer, default=0)
    total_premium_cost = Column(Float, default=0.0)
    ai_tokens_consumed = Column(Integer, default=0)
    
    # Industry and job type analysis
    top_job_titles = Column(JSON, default=list)
    top_companies = Column(JSON, default=list)
    job_type_distribution = Column(JSON, default=dict)
    
    # Performance metrics
    average_processing_time = Column(Float, default=0.0)
    total_processing_time = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, analytics_date, period_type='daily', **kwargs):
        self.user_id = user_id
        self.analytics_date = analytics_date
        self.period_type = period_type
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def calculate_success_rate(self):
        """Calculate scan success rate"""
        if self.total_scans > 0:
            return (self.successful_scans / self.total_scans) * 100
        return 0.0
    
    def to_dict(self):
        """Convert analytics to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'analytics_date': self.analytics_date.isoformat() if self.analytics_date else None,
            'period_type': self.period_type,
            'total_scans': self.total_scans,
            'successful_scans': self.successful_scans,
            'failed_scans': self.failed_scans,
            'success_rate': self.calculate_success_rate(),
            'average_match_score': self.average_match_score,
            'highest_match_score': self.highest_match_score,
            'lowest_match_score': self.lowest_match_score,
            'resumes_uploaded': self.resumes_uploaded,
            'job_descriptions_created': self.job_descriptions_created,
            'keywords_extracted': self.keywords_extracted,
            'unique_keywords': self.unique_keywords,
            'total_suggestions_generated': self.total_suggestions_generated,
            'suggestions_implemented': self.suggestions_implemented,
            'suggestions_dismissed': self.suggestions_dismissed,
            'implementation_rate': self.implementation_rate,
            'premium_scans': self.premium_scans,
            'total_premium_cost': self.total_premium_cost,
            'ai_tokens_consumed': self.ai_tokens_consumed,
            'top_job_titles': self.top_job_titles,
            'top_companies': self.top_companies,
            'job_type_distribution': self.job_type_distribution,
            'average_processing_time': self.average_processing_time,
            'total_processing_time': self.total_processing_time,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<DashboardAnalytics {self.user_id}: {self.analytics_date}>'


class UserActivity(db.Model):
    """
    User Activity Model (US-08)
    
    Tracks detailed user activities for dashboard activity feed and analytics.
    """
    
    __tablename__ = 'user_activities'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Activity information
    activity_type = Column(String(50), nullable=False)  # upload, scan, suggestion, etc.
    activity_title = Column(String(200), nullable=False)
    activity_description = Column(Text)
    
    # Related entities (optional foreign keys)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='SET NULL'), nullable=True)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='SET NULL'), nullable=True)
    scan_history_id = Column(String(36), ForeignKey('scan_history.id', ondelete='SET NULL'), nullable=True)
    
    # Activity metadata
    metadata = Column(JSON, default=dict)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    def __init__(self, user_id, activity_type, activity_title, **kwargs):
        self.user_id = user_id
        self.activity_type = activity_type
        self.activity_title = activity_title
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self):
        """Convert activity to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'activity_type': self.activity_type,
            'activity_title': self.activity_title,
            'activity_description': self.activity_description,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'scan_history_id': self.scan_history_id,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def log_activity(cls, user_id, activity_type, activity_title, **kwargs):
        """Helper method to log user activity"""
        activity = cls(user_id, activity_type, activity_title, **kwargs)
        db.session.add(activity)
        db.session.commit()
        return activity
    
    def __repr__(self):
        return f'<UserActivity {self.user_id}: {self.activity_type}>'
