"""
Dr. Resume - Subscription Models (US-10)
========================================

This module contains subscription-related database models including:
- SubscriptionPlan: Available subscription plans
- UserSubscription: User subscription tracking
"""

import uuid
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Float, JSON
from flask_sqlalchemy import SQLAlchemy

# Database instance
db = SQLAlchemy()

class SubscriptionPlan(db.Model):
    """
    Subscription Plan Model (US-10)
    
    Defines available subscription plans with features and pricing.
    """
    
    __tablename__ = 'subscription_plans'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Plan information
    name = Column(String(100), nullable=False, unique=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Pricing
    price_monthly = Column(Float, default=0.0, nullable=False)
    price_yearly = Column(Float, default=0.0, nullable=False)
    currency = Column(String(3), default='USD', nullable=False)
    
    # Plan features and limits
    max_resumes = Column(Integer, default=5)  # -1 for unlimited
    max_job_descriptions = Column(Integer, default=10)  # -1 for unlimited
    max_scans_per_month = Column(Integer, default=20)  # -1 for unlimited
    
    # Feature flags
    has_premium_suggestions = Column(Boolean, default=False)
    has_ai_analysis = Column(Boolean, default=False)
    has_advanced_analytics = Column(Boolean, default=False)
    has_export_features = Column(Boolean, default=False)
    has_priority_support = Column(Boolean, default=False)
    
    # AI usage limits
    ai_tokens_per_month = Column(Integer, default=0)  # 0 for none, -1 for unlimited
    max_premium_suggestions_per_month = Column(Integer, default=0)
    
    # Plan metadata
    features_list = Column(JSON, default=list)  # List of feature descriptions
    is_active = Column(Boolean, default=True)
    is_popular = Column(Boolean, default=False)
    sort_order = Column(Integer, default=0)
    
    # Stripe integration
    stripe_price_id_monthly = Column(String(100))
    stripe_price_id_yearly = Column(String(100))
    stripe_product_id = Column(String(100))
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user_subscriptions = db.relationship('UserSubscription', backref='plan', lazy=True)
    
    def __init__(self, name, display_name, price_monthly=0.0, price_yearly=0.0, **kwargs):
        self.name = name
        self.display_name = display_name
        self.price_monthly = price_monthly
        self.price_yearly = price_yearly
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_yearly_discount_percentage(self):
        """Calculate yearly discount percentage"""
        if self.price_monthly > 0 and self.price_yearly > 0:
            monthly_yearly = self.price_monthly * 12
            discount = ((monthly_yearly - self.price_yearly) / monthly_yearly) * 100
            return round(discount, 1)
        return 0.0
    
    def is_free_plan(self):
        """Check if this is a free plan"""
        return self.price_monthly == 0.0 and self.price_yearly == 0.0
    
    def to_dict(self):
        """Convert subscription plan to dictionary for JSON responses"""
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'price_monthly': self.price_monthly,
            'price_yearly': self.price_yearly,
            'currency': self.currency,
            'yearly_discount_percentage': self.get_yearly_discount_percentage(),
            'is_free_plan': self.is_free_plan(),
            'max_resumes': self.max_resumes,
            'max_job_descriptions': self.max_job_descriptions,
            'max_scans_per_month': self.max_scans_per_month,
            'has_premium_suggestions': self.has_premium_suggestions,
            'has_ai_analysis': self.has_ai_analysis,
            'has_advanced_analytics': self.has_advanced_analytics,
            'has_export_features': self.has_export_features,
            'has_priority_support': self.has_priority_support,
            'ai_tokens_per_month': self.ai_tokens_per_month,
            'max_premium_suggestions_per_month': self.max_premium_suggestions_per_month,
            'features_list': self.features_list,
            'is_active': self.is_active,
            'is_popular': self.is_popular,
            'sort_order': self.sort_order,
            'stripe_price_id_monthly': self.stripe_price_id_monthly,
            'stripe_price_id_yearly': self.stripe_price_id_yearly,
            'stripe_product_id': self.stripe_product_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_active_plans(cls):
        """Get all active subscription plans"""
        return cls.query.filter_by(is_active=True).order_by(cls.sort_order, cls.price_monthly).all()
    
    @classmethod
    def get_free_plan(cls):
        """Get the free plan"""
        return cls.query.filter_by(name='free', is_active=True).first()
    
    def __repr__(self):
        return f'<SubscriptionPlan {self.name}: ${self.price_monthly}/month>'


class UserSubscription(db.Model):
    """
    User Subscription Model (US-10)
    
    Tracks user subscription status, billing, and usage.
    """
    
    __tablename__ = 'user_subscriptions'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)
    subscription_plan_id = Column(String(36), ForeignKey('subscription_plans.id'), nullable=False)
    
    # Subscription status
    status = Column(String(20), default='active', nullable=False)  # active, cancelled, expired, suspended
    billing_cycle = Column(String(20), default='monthly', nullable=False)  # monthly, yearly
    
    # Billing information
    current_period_start = Column(DateTime, nullable=False)
    current_period_end = Column(DateTime, nullable=False)
    next_billing_date = Column(DateTime)
    
    # Payment information
    amount_paid = Column(Float, default=0.0)
    currency = Column(String(3), default='USD')
    payment_method = Column(String(50))  # stripe, paypal, etc.
    
    # Stripe integration
    stripe_subscription_id = Column(String(100))
    stripe_customer_id = Column(String(100))
    stripe_payment_method_id = Column(String(100))
    
    # Usage tracking (current billing period)
    scans_used_this_period = Column(Integer, default=0)
    ai_tokens_used_this_period = Column(Integer, default=0)
    premium_suggestions_used_this_period = Column(Integer, default=0)
    
    # Subscription metadata
    trial_end_date = Column(DateTime)
    is_trial = Column(Boolean, default=False)
    auto_renew = Column(Boolean, default=True)
    cancellation_date = Column(DateTime)
    cancellation_reason = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship to User
    user = db.relationship('User', backref=db.backref('subscription', uselist=False, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, subscription_plan_id, billing_cycle='monthly', **kwargs):
        self.user_id = user_id
        self.subscription_plan_id = subscription_plan_id
        self.billing_cycle = billing_cycle
        
        # Set billing period
        now = datetime.utcnow()
        self.current_period_start = now
        
        if billing_cycle == 'yearly':
            self.current_period_end = now + timedelta(days=365)
            self.next_billing_date = self.current_period_end
        else:  # monthly
            self.current_period_end = now + timedelta(days=30)
            self.next_billing_date = self.current_period_end
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def is_active(self):
        """Check if subscription is currently active"""
        return (self.status == 'active' and 
                datetime.utcnow() <= self.current_period_end)
    
    def is_expired(self):
        """Check if subscription has expired"""
        return datetime.utcnow() > self.current_period_end
    
    def days_until_renewal(self):
        """Get days until next billing date"""
        if self.next_billing_date:
            delta = self.next_billing_date - datetime.utcnow()
            return max(0, delta.days)
        return 0
    
    def can_use_feature(self, feature_name):
        """Check if user can use a specific feature"""
        if not self.is_active():
            return False
        
        plan = self.plan
        if not plan:
            return False
        
        feature_map = {
            'premium_suggestions': plan.has_premium_suggestions,
            'ai_analysis': plan.has_ai_analysis,
            'advanced_analytics': plan.has_advanced_analytics,
            'export_features': plan.has_export_features,
            'priority_support': plan.has_priority_support
        }
        
        return feature_map.get(feature_name, False)
    
    def get_usage_percentage(self, usage_type):
        """Get usage percentage for current billing period"""
        plan = self.plan
        if not plan:
            return 0.0
        
        if usage_type == 'scans':
            if plan.max_scans_per_month == -1:  # Unlimited
                return 0.0
            if plan.max_scans_per_month == 0:
                return 100.0
            return min(100.0, (self.scans_used_this_period / plan.max_scans_per_month) * 100)
        
        elif usage_type == 'ai_tokens':
            if plan.ai_tokens_per_month == -1:  # Unlimited
                return 0.0
            if plan.ai_tokens_per_month == 0:
                return 100.0
            return min(100.0, (self.ai_tokens_used_this_period / plan.ai_tokens_per_month) * 100)
        
        elif usage_type == 'premium_suggestions':
            if plan.max_premium_suggestions_per_month == -1:  # Unlimited
                return 0.0
            if plan.max_premium_suggestions_per_month == 0:
                return 100.0
            return min(100.0, (self.premium_suggestions_used_this_period / plan.max_premium_suggestions_per_month) * 100)
        
        return 0.0
    
    def reset_usage_counters(self):
        """Reset usage counters for new billing period"""
        self.scans_used_this_period = 0
        self.ai_tokens_used_this_period = 0
        self.premium_suggestions_used_this_period = 0
        db.session.commit()
    
    def to_dict(self):
        """Convert subscription to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'subscription_plan_id': self.subscription_plan_id,
            'status': self.status,
            'billing_cycle': self.billing_cycle,
            'is_active': self.is_active(),
            'is_expired': self.is_expired(),
            'current_period_start': self.current_period_start.isoformat() if self.current_period_start else None,
            'current_period_end': self.current_period_end.isoformat() if self.current_period_end else None,
            'next_billing_date': self.next_billing_date.isoformat() if self.next_billing_date else None,
            'days_until_renewal': self.days_until_renewal(),
            'amount_paid': self.amount_paid,
            'currency': self.currency,
            'payment_method': self.payment_method,
            'scans_used_this_period': self.scans_used_this_period,
            'ai_tokens_used_this_period': self.ai_tokens_used_this_period,
            'premium_suggestions_used_this_period': self.premium_suggestions_used_this_period,
            'scans_usage_percentage': self.get_usage_percentage('scans'),
            'ai_tokens_usage_percentage': self.get_usage_percentage('ai_tokens'),
            'premium_suggestions_usage_percentage': self.get_usage_percentage('premium_suggestions'),
            'trial_end_date': self.trial_end_date.isoformat() if self.trial_end_date else None,
            'is_trial': self.is_trial,
            'auto_renew': self.auto_renew,
            'cancellation_date': self.cancellation_date.isoformat() if self.cancellation_date else None,
            'cancellation_reason': self.cancellation_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_subscription(cls, user_id, subscription_plan_id, billing_cycle='monthly', **kwargs):
        """Create a new user subscription"""
        subscription = cls(
            user_id=user_id,
            subscription_plan_id=subscription_plan_id,
            billing_cycle=billing_cycle,
            **kwargs
        )
        db.session.add(subscription)
        db.session.commit()
        return subscription
    
    def __repr__(self):
        return f'<UserSubscription {self.user_id}: {self.status}>'
