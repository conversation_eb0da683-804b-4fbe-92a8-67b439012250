"""
Dr. Resume - Suggestion Models (US-07)
======================================

This module contains suggestion-related database models including:
- Suggestion: Basic improvement suggestions
- PremiumSuggestion: AI-powered premium suggestions with OpenAI integration
"""

import uuid
from datetime import datetime
from enum import Enum
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Float, JSON, Enum as SQLEnum
from flask_sqlalchemy import SQLAlchemy

# Database instance will be injected by app
db = None

class SuggestionType(Enum):
    """Enumeration for suggestion types"""
    KEYWORD_MISSING = "keyword_missing"
    SKILL_ENHANCEMENT = "skill_enhancement"
    EXPERIENCE_IMPROVEMENT = "experience_improvement"
    EDUCATION_ADDITION = "education_addition"
    FORMAT_IMPROVEMENT = "format_improvement"
    CONTENT_OPTIMIZATION = "content_optimization"
    ATS_OPTIMIZATION = "ats_optimization"

class SuggestionPriority(Enum):
    """Enumeration for suggestion priorities"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class Suggestion(db.Model):
    """
    Basic Suggestion Model (US-07)
    
    Stores basic improvement suggestions generated from local logic and keyword analysis.
    These are free suggestions available to all users.
    """
    
    __tablename__ = 'suggestions'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)
    matching_score_id = Column(String(36), ForeignKey('matching_scores.id', ondelete='CASCADE'), nullable=True)
    
    # Suggestion content
    suggestion_type = Column(SQLEnum(SuggestionType), nullable=False)
    priority = Column(SQLEnum(SuggestionPriority), default=SuggestionPriority.MEDIUM)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    
    # Missing keywords (JSON array)
    missing_keywords = Column(JSON, default=list)
    
    # Suggested keywords to add (JSON array)
    suggested_keywords = Column(JSON, default=list)
    
    # Improvement areas (JSON object)
    improvement_areas = Column(JSON, default=dict)
    
    # Confidence score (0.0 to 1.0)
    confidence_score = Column(Float, default=0.0)
    
    # Implementation difficulty (1-5 scale)
    implementation_difficulty = Column(Integer, default=3)
    
    # Expected impact on matching score (percentage)
    expected_impact = Column(Float, default=0.0)
    
    # Status tracking
    is_implemented = Column(Boolean, default=False)
    is_dismissed = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, resume_id, job_description_id, suggestion_type, title, description, **kwargs):
        self.user_id = user_id
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.suggestion_type = suggestion_type
        self.title = title
        self.description = description
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_priority_color(self):
        """Get color code for priority level"""
        priority_colors = {
            SuggestionPriority.LOW: '#28a745',      # Green
            SuggestionPriority.MEDIUM: '#ffc107',   # Yellow
            SuggestionPriority.HIGH: '#fd7e14',     # Orange
            SuggestionPriority.CRITICAL: '#dc3545'  # Red
        }
        return priority_colors.get(self.priority, '#6c757d')
    
    def get_difficulty_text(self):
        """Get text description for implementation difficulty"""
        difficulty_map = {
            1: 'Very Easy',
            2: 'Easy',
            3: 'Moderate',
            4: 'Hard',
            5: 'Very Hard'
        }
        return difficulty_map.get(self.implementation_difficulty, 'Unknown')
    
    def to_dict(self):
        """Convert suggestion to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'matching_score_id': self.matching_score_id,
            'suggestion_type': self.suggestion_type.value if self.suggestion_type else None,
            'priority': self.priority.value if self.priority else None,
            'priority_color': self.get_priority_color(),
            'title': self.title,
            'description': self.description,
            'missing_keywords': self.missing_keywords,
            'suggested_keywords': self.suggested_keywords,
            'improvement_areas': self.improvement_areas,
            'confidence_score': self.confidence_score,
            'implementation_difficulty': self.implementation_difficulty,
            'difficulty_text': self.get_difficulty_text(),
            'expected_impact': self.expected_impact,
            'is_implemented': self.is_implemented,
            'is_dismissed': self.is_dismissed,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_suggestion(cls, user_id, resume_id, job_description_id, suggestion_type, title, description, **kwargs):
        """Create a new suggestion record"""
        suggestion = cls(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            suggestion_type=suggestion_type,
            title=title,
            description=description,
            **kwargs
        )
        db.session.add(suggestion)
        db.session.commit()
        return suggestion
    
    @classmethod
    def get_user_suggestions(cls, user_id, resume_id=None, job_description_id=None, active_only=True):
        """Get suggestions for a user"""
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        if active_only:
            query = query.filter_by(is_dismissed=False)
        
        return query.order_by(cls.priority.desc(), cls.confidence_score.desc()).all()
    
    def __repr__(self):
        return f'<Suggestion {self.id}: {self.title}>'


class PremiumSuggestion(db.Model):
    """
    Premium AI-Powered Suggestion Model (US-07)
    
    Stores premium suggestions generated using OpenAI API with detailed analysis,
    personalized recommendations, and industry insights.
    """
    
    __tablename__ = 'premium_suggestions'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)
    matching_score_id = Column(String(36), ForeignKey('matching_scores.id', ondelete='CASCADE'), nullable=True)
    
    # Basic suggestion information (inherited from Suggestion)
    suggestion_type = Column(SQLEnum(SuggestionType), nullable=False)
    priority = Column(SQLEnum(SuggestionPriority), default=SuggestionPriority.HIGH)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    
    # AI-generated content
    ai_analysis = Column(Text, nullable=False)
    detailed_recommendations = Column(JSON, default=list)
    personalized_tips = Column(JSON, default=list)
    industry_insights = Column(JSON, default=dict)
    
    # AI model information
    ai_model_used = Column(String(100), default='gpt-3.5-turbo')
    ai_prompt_version = Column(String(50), default='v1.0')
    ai_response_tokens = Column(Integer, default=0)
    ai_cost_estimate = Column(Float, default=0.0)
    
    # Enhanced metrics
    market_relevance_score = Column(Float, default=0.0)
    career_progression_impact = Column(Float, default=0.0)
    salary_impact_estimate = Column(Float, default=0.0)
    
    # Implementation guidance
    step_by_step_guide = Column(JSON, default=list)
    resources_and_links = Column(JSON, default=list)
    timeline_estimate = Column(String(100))
    
    # Status tracking
    is_implemented = Column(Boolean, default=False)
    is_dismissed = Column(Boolean, default=False)
    user_rating = Column(Integer)  # 1-5 star rating
    user_feedback = Column(Text)
    
    # Processing metadata
    generation_time_seconds = Column(Float)
    prompt_used = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, resume_id, job_description_id, suggestion_type, title, description, ai_analysis, **kwargs):
        self.user_id = user_id
        self.resume_id = resume_id
        self.job_description_id = job_description_id
        self.suggestion_type = suggestion_type
        self.title = title
        self.description = description
        self.ai_analysis = ai_analysis
        
        # Set other attributes from kwargs
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_cost_formatted(self):
        """Get formatted cost estimate"""
        return f"${self.ai_cost_estimate:.4f}" if self.ai_cost_estimate else "$0.0000"
    
    def get_rating_stars(self):
        """Get star rating as string"""
        if self.user_rating:
            return "★" * self.user_rating + "☆" * (5 - self.user_rating)
        return "Not rated"
    
    def to_dict(self):
        """Convert premium suggestion to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'matching_score_id': self.matching_score_id,
            'suggestion_type': self.suggestion_type.value if self.suggestion_type else None,
            'priority': self.priority.value if self.priority else None,
            'title': self.title,
            'description': self.description,
            'ai_analysis': self.ai_analysis,
            'detailed_recommendations': self.detailed_recommendations,
            'personalized_tips': self.personalized_tips,
            'industry_insights': self.industry_insights,
            'ai_model_used': self.ai_model_used,
            'ai_prompt_version': self.ai_prompt_version,
            'ai_response_tokens': self.ai_response_tokens,
            'ai_cost_estimate': self.ai_cost_estimate,
            'cost_formatted': self.get_cost_formatted(),
            'market_relevance_score': self.market_relevance_score,
            'career_progression_impact': self.career_progression_impact,
            'salary_impact_estimate': self.salary_impact_estimate,
            'step_by_step_guide': self.step_by_step_guide,
            'resources_and_links': self.resources_and_links,
            'timeline_estimate': self.timeline_estimate,
            'is_implemented': self.is_implemented,
            'is_dismissed': self.is_dismissed,
            'user_rating': self.user_rating,
            'rating_stars': self.get_rating_stars(),
            'user_feedback': self.user_feedback,
            'generation_time_seconds': self.generation_time_seconds,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_premium_suggestion(cls, user_id, resume_id, job_description_id, suggestion_type, title, description, ai_analysis, **kwargs):
        """Create a new premium suggestion record"""
        suggestion = cls(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            suggestion_type=suggestion_type,
            title=title,
            description=description,
            ai_analysis=ai_analysis,
            **kwargs
        )
        db.session.add(suggestion)
        db.session.commit()
        return suggestion
    
    @classmethod
    def get_user_premium_suggestions(cls, user_id, resume_id=None, job_description_id=None, active_only=True):
        """Get premium suggestions for a user"""
        query = cls.query.filter_by(user_id=user_id)
        
        if resume_id:
            query = query.filter_by(resume_id=resume_id)
        if job_description_id:
            query = query.filter_by(job_description_id=job_description_id)
        if active_only:
            query = query.filter_by(is_dismissed=False)
        
        return query.order_by(cls.priority.desc(), cls.market_relevance_score.desc()).all()
    
    def __repr__(self):
        return f'<PremiumSuggestion {self.id}: {self.title}>'
