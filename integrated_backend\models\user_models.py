"""
Dr. Resume - User Models (US-01 & US-10)
========================================

This module contains user-related database models including:
- User: Core authentication and profile (US-01)
- UserProfile: Extended profile information (US-10)
- UserPreferences: Application preferences (US-10)
- AccountActivity: Account activity logging (US-10)
"""

import uuid
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Float, ForeignKey
from flask_sqlalchemy import SQLAlchemy

# Database instance will be injected by app
db = None

class User(db.Model):
    """
    Core User Model (US-01)
    
    Stores essential user information for authentication and basic profile data.
    This is the foundation model that all other features reference.
    """
    
    __tablename__ = 'users'
    
    # Primary key - UUID for security
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Authentication credentials
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Basic profile information
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    
    # Account status and role
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    role = Column(String(20), default='user', nullable=False)  # user, premium, admin
    is_premium = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # Email verification
    email_verification_token = Column(String(100), nullable=True)
    email_verified_at = Column(DateTime, nullable=True)
    
    # Password reset
    password_reset_token = Column(String(100), nullable=True)
    password_reset_expires = Column(DateTime, nullable=True)
    
    # Relationships
    resumes = db.relationship('Resume', backref='user', lazy=True, cascade='all, delete-orphan')
    job_descriptions = db.relationship('JobDescription', backref='user', lazy=True, cascade='all, delete-orphan')
    keywords = db.relationship('Keyword', backref='user', lazy=True, cascade='all, delete-orphan')
    matching_scores = db.relationship('MatchingScore', backref='user', lazy=True, cascade='all, delete-orphan')
    suggestions = db.relationship('Suggestion', backref='user', lazy=True, cascade='all, delete-orphan')
    premium_suggestions = db.relationship('PremiumSuggestion', backref='user', lazy=True, cascade='all, delete-orphan')
    scan_history = db.relationship('ScanHistory', backref='user', lazy=True, cascade='all, delete-orphan')
    user_activities = db.relationship('UserActivity', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, email, password, first_name=None, last_name=None):
        self.email = email.lower().strip()
        self.set_password(password)
        self.first_name = first_name
        self.last_name = last_name
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self):
        """Get user's full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email.split('@')[0]
    
    def to_dict(self):
        """Convert user to dictionary for JSON responses"""
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.get_full_name(),
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'is_premium': self.is_premium,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    @classmethod
    def find_by_email(cls, email):
        """Find user by email address"""
        return cls.query.filter_by(email=email.lower().strip()).first()
    
    @classmethod
    def find_by_id(cls, user_id):
        """Find user by ID"""
        return cls.query.filter_by(id=user_id).first()
    
    def __repr__(self):
        return f'<User {self.email}>'


class UserProfile(db.Model):
    """
    Extended User Profile Model (US-10)
    
    Stores additional profile information beyond basic authentication data.
    """
    
    __tablename__ = 'user_profiles'
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)
    
    # Extended profile information
    phone_number = Column(String(20), nullable=True)
    date_of_birth = Column(DateTime, nullable=True)
    location = Column(String(100), nullable=True)
    timezone = Column(String(50), default='UTC', nullable=True)
    
    # Professional information
    current_job_title = Column(String(100), nullable=True)
    current_company = Column(String(100), nullable=True)
    industry = Column(String(50), nullable=True)
    experience_years = Column(Integer, nullable=True)
    
    # Profile content
    bio = Column(Text, nullable=True)
    website_url = Column(String(200), nullable=True)
    linkedin_url = Column(String(200), nullable=True)
    github_url = Column(String(200), nullable=True)
    
    # Profile settings
    profile_picture_url = Column(String(500), nullable=True)
    is_profile_complete = Column(Boolean, default=False, nullable=False)
    profile_completion_percentage = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship to User
    user = db.relationship('User', backref=db.backref('profile', uselist=False, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, **kwargs):
        self.user_id = user_id
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.calculate_completion_percentage()
    
    def calculate_completion_percentage(self):
        """Calculate profile completion percentage"""
        fields = [
            self.phone_number, self.location, self.current_job_title,
            self.current_company, self.industry, self.bio
        ]
        completed_fields = sum(1 for field in fields if field)
        self.profile_completion_percentage = int((completed_fields / len(fields)) * 100)
        self.is_profile_complete = self.profile_completion_percentage >= 80
    
    def to_dict(self):
        """Convert profile to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'phone_number': self.phone_number,
            'location': self.location,
            'current_job_title': self.current_job_title,
            'current_company': self.current_company,
            'industry': self.industry,
            'experience_years': self.experience_years,
            'bio': self.bio,
            'website_url': self.website_url,
            'linkedin_url': self.linkedin_url,
            'github_url': self.github_url,
            'profile_completion_percentage': self.profile_completion_percentage,
            'is_profile_complete': self.is_profile_complete,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class UserPreferences(db.Model):
    """
    User Application Preferences Model (US-10)
    
    Stores user preferences for application behavior and settings.
    """
    
    __tablename__ = 'user_preferences'
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)
    
    # Notification preferences
    email_notifications = Column(Boolean, default=True, nullable=False)
    marketing_emails = Column(Boolean, default=False, nullable=False)
    scan_completion_notifications = Column(Boolean, default=True, nullable=False)
    weekly_summary_emails = Column(Boolean, default=True, nullable=False)
    
    # Privacy settings
    profile_visibility = Column(String(20), default='private', nullable=False)  # public, private, contacts
    data_sharing = Column(Boolean, default=False, nullable=False)
    analytics_tracking = Column(Boolean, default=True, nullable=False)
    
    # Application preferences
    theme = Column(String(20), default='light', nullable=False)  # light, dark, auto
    dashboard_layout = Column(String(20), default='default', nullable=False)
    items_per_page = Column(Integer, default=10, nullable=False)
    auto_save = Column(Boolean, default=True, nullable=False)
    
    # AI and processing preferences
    ai_suggestions_enabled = Column(Boolean, default=True, nullable=False)
    processing_quality = Column(String(20), default='standard', nullable=False)  # fast, standard, detailed
    auto_keyword_extraction = Column(Boolean, default=True, nullable=False)
    
    # Security preferences
    two_factor_enabled = Column(Boolean, default=False, nullable=False)
    session_timeout_minutes = Column(Integer, default=60, nullable=False)
    login_notifications = Column(Boolean, default=True, nullable=False)
    
    # Custom preferences (JSON field for extensibility)
    custom_preferences = Column(JSON, default=dict, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship to User
    user = db.relationship('User', backref=db.backref('preferences', uselist=False, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, **kwargs):
        self.user_id = user_id
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self):
        """Convert preferences to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'email_notifications': self.email_notifications,
            'marketing_emails': self.marketing_emails,
            'scan_completion_notifications': self.scan_completion_notifications,
            'weekly_summary_emails': self.weekly_summary_emails,
            'profile_visibility': self.profile_visibility,
            'data_sharing': self.data_sharing,
            'analytics_tracking': self.analytics_tracking,
            'theme': self.theme,
            'dashboard_layout': self.dashboard_layout,
            'items_per_page': self.items_per_page,
            'auto_save': self.auto_save,
            'ai_suggestions_enabled': self.ai_suggestions_enabled,
            'processing_quality': self.processing_quality,
            'auto_keyword_extraction': self.auto_keyword_extraction,
            'two_factor_enabled': self.two_factor_enabled,
            'session_timeout_minutes': self.session_timeout_minutes,
            'login_notifications': self.login_notifications,
            'custom_preferences': self.custom_preferences,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class AccountActivity(db.Model):
    """
    Account Activity Logging Model (US-10)
    
    Tracks user account activities for security and audit purposes.
    """
    
    __tablename__ = 'account_activities'
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key to users table
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Activity information
    activity_type = Column(String(50), nullable=False)  # login, logout, password_change, etc.
    description = Column(Text, nullable=False)
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    
    # Location information (if available)
    country = Column(String(50), nullable=True)
    city = Column(String(100), nullable=True)
    
    # Status and metadata
    status = Column(String(20), default='success', nullable=False)  # success, failed, suspicious
    metadata = Column(JSON, default=dict, nullable=False)
    
    # Timestamp
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationship to User
    user = db.relationship('User', backref=db.backref('account_activities', lazy=True, cascade='all, delete-orphan'))
    
    def __init__(self, user_id, activity_type, description, **kwargs):
        self.user_id = user_id
        self.activity_type = activity_type
        self.description = description
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self):
        """Convert activity to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'activity_type': self.activity_type,
            'description': self.description,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'country': self.country,
            'city': self.city,
            'status': self.status,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def log_activity(cls, user_id, activity_type, description, **kwargs):
        """Helper method to log user activity"""
        activity = cls(user_id, activity_type, description, **kwargs)
        db.session.add(activity)
        db.session.commit()
        return activity
