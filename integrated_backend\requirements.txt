# Dr. Resume - Integrated Backend Requirements
# ============================================
# 
# This file contains all Python dependencies for the integrated backend
# that combines all US-01 to US-10 features.

# Core Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
Flask-Limiter==3.5.0
Flask-Mail==0.9.1

# Database
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7  # PostgreSQL adapter
alembic==1.12.0  # Database migrations

# Security and Authentication
Werkzeug==2.3.7
bcrypt==4.0.1
cryptography==41.0.4

# File Processing
PyPDF2==3.0.1  # PDF text extraction
python-docx==0.8.11  # DOCX text extraction

# Natural Language Processing
spacy==3.6.1  # Primary NLP library
nltk==3.8.1  # Fallback NLP library
scikit-learn==1.3.0  # Machine learning for text analysis

# AI Integration (Premium Features)
openai==0.28.1  # OpenAI API for premium suggestions
tiktoken==0.5.1  # Token counting for OpenAI

# Data Processing and Analysis
pandas==2.1.1  # Data manipulation
numpy==1.25.2  # Numerical computing
python-dateutil==2.8.2  # Date utilities

# Environment and Configuration
python-dotenv==1.0.0  # Environment variable management
click==8.1.7  # CLI utilities

# HTTP and API
requests==2.31.0  # HTTP requests
urllib3==2.0.4  # HTTP client

# Caching and Performance
redis==4.6.0  # Redis client for caching and rate limiting

# Development and Testing
pytest==7.4.2  # Testing framework
pytest-flask==1.2.0  # Flask testing utilities
pytest-cov==4.1.0  # Coverage reporting
factory-boy==3.3.0  # Test data factories

# Production Server
gunicorn==21.2.0  # WSGI HTTP Server
gevent==23.7.0  # Async networking library

# Monitoring and Logging
sentry-sdk[flask]==1.32.0  # Error tracking

# File and Path Utilities
pathlib2==2.3.7  # Path utilities (Python 2/3 compatibility)

# JSON and Data Serialization
marshmallow==3.20.1  # Object serialization
marshmallow-sqlalchemy==0.29.0  # SQLAlchemy integration

# Validation
email-validator==2.0.0  # Email validation
phonenumbers==8.13.19  # Phone number validation

# Image Processing (for future profile pictures)
Pillow==10.0.0  # Image processing

# Excel/CSV Export (for dashboard exports)
openpyxl==3.1.2  # Excel file handling
xlsxwriter==3.1.9  # Excel writing

# Background Tasks (for async processing)
celery==5.3.1  # Distributed task queue
redis==4.6.0  # Message broker for Celery

# API Documentation
flask-restx==1.1.0  # API documentation and validation
flasgger==0.9.7.1  # Swagger UI integration

# Rate Limiting and Security
slowapi==0.1.9  # Rate limiting
python-jose[cryptography]==3.3.0  # JWT handling

# Text Processing and Similarity
textdistance==4.6.0  # Text similarity algorithms
fuzzywuzzy==0.18.0  # Fuzzy string matching
python-Levenshtein==0.21.1  # Fast string similarity

# Date and Time
pytz==2023.3  # Timezone handling
arrow==1.2.3  # Better date/time handling

# Configuration Management
pydantic==2.3.0  # Data validation and settings management

# Development Tools
black==23.7.0  # Code formatting
flake8==6.0.0  # Code linting
isort==5.12.0  # Import sorting

# Optional: spaCy English model (install separately)
# python -m spacy download en_core_web_sm

# Optional: NLTK data (downloaded programmatically in code)
# punkt, stopwords, averaged_perceptron_tagger
