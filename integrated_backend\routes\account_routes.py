"""
Dr. Resume - Account Routes (US-10)
===================================

This module contains account management routes.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user_models import User, UserProfile, UserPreferences, db
from models.subscription_models import UserSubscription, SubscriptionPlan

account_bp = Blueprint('account', __name__)

@account_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get user profile"""
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user:
            return jsonify({'success': False, 'message': 'User not found'}), 404
        
        # Get profile
        profile = UserProfile.query.filter_by(user_id=current_user_id).first()
        
        return jsonify({
            'success': True,
            'user': user.to_dict(),
            'profile': profile.to_dict() if profile else None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get profile error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to get profile'}), 500

@account_bp.route('/preferences', methods=['GET'])
@jwt_required()
def get_preferences():
    """Get user preferences"""
    try:
        current_user_id = get_jwt_identity()
        
        preferences = UserPreferences.query.filter_by(user_id=current_user_id).first()
        
        return jsonify({
            'success': True,
            'preferences': preferences.to_dict() if preferences else {}
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get preferences error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to get preferences'}), 500

@account_bp.route('/subscription', methods=['GET'])
@jwt_required()
def get_subscription():
    """Get user subscription"""
    try:
        current_user_id = get_jwt_identity()
        
        subscription = UserSubscription.query.filter_by(user_id=current_user_id).first()
        
        if subscription:
            # Get plan details
            plan = SubscriptionPlan.query.get(subscription.subscription_plan_id)
            subscription_data = subscription.to_dict()
            subscription_data['plan'] = plan.to_dict() if plan else None
        else:
            subscription_data = None
        
        return jsonify({
            'success': True,
            'subscription': subscription_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get subscription error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to get subscription'}), 500
