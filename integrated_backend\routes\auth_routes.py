"""
Dr. Resume - Authentication Routes (US-01 & US-02)
==================================================

This module contains authentication-related routes including:
- User registration (US-01)
- Login with JWT tokens (US-02)
- Token refresh and validation
- Password reset functionality
"""

import re
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import (
    create_access_token, create_refresh_token, jwt_required, 
    get_jwt_identity, get_jwt, verify_jwt_in_request
)
from werkzeug.security import check_password_hash
from models.user_models import User, UserProfile, UserPreferences, AccountActivity, db

# Create blueprint
auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 6:
        return False, "Password must be at least 6 characters long"
    
    if not re.search(r'[A-Za-z]', password):
        return False, "Password must contain at least one letter"
    
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    
    return True, "Password is valid"

@auth_bp.route('/register', methods=['POST'])
def register():
    """
    User Registration Endpoint (US-01)
    
    Registers a new user with email and password validation.
    Creates user profile and preferences automatically.
    """
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        
        # Validate email
        if not email:
            return jsonify({
                'success': False,
                'message': 'Email is required'
            }), 400
        
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format'
            }), 400
        
        # Check if email already exists
        existing_user = User.find_by_email(email)
        if existing_user:
            return jsonify({
                'success': False,
                'message': 'Email already registered'
            }), 409
        
        # Validate password
        if not password:
            return jsonify({
                'success': False,
                'message': 'Password is required'
            }), 400
        
        is_valid, message = validate_password(password)
        if not is_valid:
            return jsonify({
                'success': False,
                'message': message
            }), 400
        
        # Create new user
        user = User(
            email=email,
            password=password,
            first_name=first_name if first_name else None,
            last_name=last_name if last_name else None
        )
        
        db.session.add(user)
        db.session.flush()  # Get user ID
        
        # Create user profile
        profile = UserProfile(user_id=user.id)
        db.session.add(profile)
        
        # Create user preferences with defaults
        preferences = UserPreferences(user_id=user.id)
        db.session.add(preferences)
        
        # Log registration activity
        activity = AccountActivity(
            user_id=user.id,
            activity_type='registration',
            description='User account created',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            status='success'
        )
        db.session.add(activity)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Account created successfully',
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Registration error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Registration failed. Please try again.'
        }), 500

@auth_bp.route('/check-email', methods=['POST'])
def check_email():
    """
    Email Availability Check Endpoint (US-01)
    
    Checks if an email address is available for registration.
    """
    try:
        data = request.get_json()
        
        if not data or 'email' not in data:
            return jsonify({
                'success': False,
                'message': 'Email is required'
            }), 400
        
        email = data['email'].strip().lower()
        
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format',
                'available': False
            }), 400
        
        # Check if email exists
        existing_user = User.find_by_email(email)
        available = existing_user is None
        
        return jsonify({
            'success': True,
            'email': email,
            'available': available,
            'message': 'Email is available' if available else 'Email is already registered'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Email check error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Email check failed'
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """
    User Login Endpoint (US-02)
    
    Authenticates user and returns JWT tokens.
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        # Find user by email
        user = User.find_by_email(email)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401
        
        # Check if user account is active
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': 'Account is deactivated. Please contact support.'
            }), 401
        
        # Verify password
        if not user.check_password(password):
            # Log failed login attempt
            AccountActivity.log_activity(
                user_id=user.id,
                activity_type='login_failed',
                description='Failed login attempt',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                status='failed'
            )
            
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401
        
        # Update last login timestamp
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # Create JWT tokens
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'email': user.email,
                'role': user.role,
                'is_premium': user.is_premium
            },
            expires_delta=timedelta(hours=1)
        )
        
        refresh_token = create_refresh_token(
            identity=user.id,
            expires_delta=timedelta(days=30)
        )
        
        # Log successful login
        AccountActivity.log_activity(
            user_id=user.id,
            activity_type='login',
            description='Successful login',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            status='success'
        )
        
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': user.to_dict(),
            'tokens': {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'token_type': 'Bearer',
                'expires_in': 3600  # 1 hour in seconds
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Login error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Login failed. Please try again.'
        }), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """
    Token Refresh Endpoint (US-02)
    
    Refreshes access token using refresh token.
    """
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user or not user.is_active:
            return jsonify({
                'success': False,
                'message': 'User not found or inactive'
            }), 401
        
        # Create new access token
        new_access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'email': user.email,
                'role': user.role,
                'is_premium': user.is_premium
            },
            expires_delta=timedelta(hours=1)
        )
        
        return jsonify({
            'success': True,
            'access_token': new_access_token,
            'token_type': 'Bearer',
            'expires_in': 3600
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Token refresh error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Token refresh failed'
        }), 500

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get Current User Endpoint (US-02)
    
    Returns current user information from JWT token.
    """
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        return jsonify({
            'success': True,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get current user error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to get user information'
        }), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """
    User Logout Endpoint (US-02)
    
    Logs out user and invalidates token (in a real implementation,
    you would add the token to a blacklist).
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Log logout activity
        AccountActivity.log_activity(
            user_id=current_user_id,
            activity_type='logout',
            description='User logged out',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            status='success'
        )
        
        return jsonify({
            'success': True,
            'message': 'Logged out successfully'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Logout error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Logout failed'
        }), 500

@auth_bp.route('/validate-token', methods=['POST'])
@jwt_required()
def validate_token():
    """
    Token Validation Endpoint (US-02)
    
    Validates if the current token is valid and returns user info.
    """
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user or not user.is_active:
            return jsonify({
                'success': False,
                'message': 'Invalid token or user inactive',
                'valid': False
            }), 401
        
        return jsonify({
            'success': True,
            'message': 'Token is valid',
            'valid': True,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Token validation error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Token validation failed',
            'valid': False
        }), 500
