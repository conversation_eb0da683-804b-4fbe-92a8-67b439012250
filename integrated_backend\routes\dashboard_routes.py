"""
Dr. Resume - Dashboard Routes (US-08)
=====================================

This module contains dashboard and analytics routes.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user_models import User
from models.dashboard_models import Scan<PERSON>istory, DashboardAnalytics, UserActivity
from models.content_models import Resume, JobDescription
from models.analysis_models import MatchingScore
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/overview', methods=['GET'])
@jwt_required()
def get_dashboard_overview():
    """Get dashboard overview statistics"""
    try:
        current_user_id = get_jwt_identity()
        
        # Get basic counts
        total_resumes = Resume.query.filter_by(user_id=current_user_id, is_active=True).count()
        total_jds = JobDescription.query.filter_by(user_id=current_user_id, is_active=True).count()
        total_scans = MatchingScore.query.filter_by(user_id=current_user_id).count()
        
        # Get recent activity
        recent_activity = UserActivity.query.filter_by(user_id=current_user_id)\
                                          .order_by(UserActivity.created_at.desc())\
                                          .limit(5).all()
        
        # Calculate average match score
        avg_match = 0.0
        if total_scans > 0:
            scores = MatchingScore.query.filter_by(user_id=current_user_id).all()
            if scores:
                avg_match = sum(float(score.overall_match_percentage) for score in scores) / len(scores)
        
        # Get this month's stats
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_scans = MatchingScore.query.filter(
            MatchingScore.user_id == current_user_id,
            MatchingScore.created_at >= month_start
        ).count()
        
        return jsonify({
            'success': True,
            'overview': {
                'total_resumes': total_resumes,
                'total_job_descriptions': total_jds,
                'total_scans': total_scans,
                'monthly_scans': monthly_scans,
                'average_match_score': round(avg_match, 1),
                'recent_activity': [activity.to_dict() for activity in recent_activity]
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Dashboard overview error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to load dashboard overview'}), 500

@dashboard_bp.route('/history', methods=['GET'])
@jwt_required()
def get_scan_history():
    """Get scan history"""
    try:
        current_user_id = get_jwt_identity()
        limit = int(request.args.get('limit', 10))
        offset = int(request.args.get('offset', 0))
        
        # Get matching scores as scan history
        scans = MatchingScore.query.filter_by(user_id=current_user_id)\
                                  .order_by(MatchingScore.created_at.desc())\
                                  .offset(offset).limit(limit).all()
        
        # Convert to scan history format
        scan_history = []
        for scan in scans:
            # Get resume and JD info
            resume = Resume.query.get(scan.resume_id)
            jd = JobDescription.query.get(scan.job_description_id)
            
            scan_data = {
                'id': scan.id,
                'scan_title': f"{resume.resume_title if resume else 'Resume'} vs {jd.title if jd else 'Job Description'}",
                'overall_match_percentage': float(scan.overall_match_percentage),
                'match_grade': scan.get_match_grade(),
                'match_status': scan.get_match_status(),
                'resume_name': resume.resume_title if resume else 'Unknown',
                'job_title': jd.title if jd else 'Unknown',
                'company_name': jd.company_name if jd else None,
                'created_at': scan.created_at.isoformat() if scan.created_at else None,
                'keyword_overlap_count': scan.keyword_overlap_count,
                'missing_keywords_count': len(scan.missing_keywords) if scan.missing_keywords else 0
            }
            scan_history.append(scan_data)
        
        total_scans = MatchingScore.query.filter_by(user_id=current_user_id).count()
        
        return jsonify({
            'success': True,
            'scan_history': scan_history,
            'total': total_scans,
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Scan history error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to load scan history'}), 500

@dashboard_bp.route('/analytics', methods=['GET'])
@jwt_required()
def get_analytics():
    """Get analytics data"""
    try:
        current_user_id = get_jwt_identity()
        period = request.args.get('period', 'monthly')
        
        # Calculate date range based on period
        now = datetime.utcnow()
        if period == 'weekly':
            start_date = now - timedelta(weeks=4)
        elif period == 'yearly':
            start_date = now - timedelta(days=365)
        else:  # monthly
            start_date = now - timedelta(days=30)
        
        # Get scans in period
        scans = MatchingScore.query.filter(
            MatchingScore.user_id == current_user_id,
            MatchingScore.created_at >= start_date
        ).all()
        
        # Calculate analytics
        analytics = {
            'period': period,
            'total_scans': len(scans),
            'average_score': 0.0,
            'highest_score': 0.0,
            'lowest_score': 100.0,
            'score_distribution': {
                'excellent': 0,  # 90+
                'good': 0,       # 80-89
                'fair': 0,       # 70-79
                'poor': 0        # <70
            },
            'daily_scans': [],
            'top_keywords': [],
            'improvement_trend': 0.0
        }
        
        if scans:
            scores = [float(scan.overall_match_percentage) for scan in scans]
            analytics['average_score'] = round(sum(scores) / len(scores), 1)
            analytics['highest_score'] = round(max(scores), 1)
            analytics['lowest_score'] = round(min(scores), 1)
            
            # Score distribution
            for score in scores:
                if score >= 90:
                    analytics['score_distribution']['excellent'] += 1
                elif score >= 80:
                    analytics['score_distribution']['good'] += 1
                elif score >= 70:
                    analytics['score_distribution']['fair'] += 1
                else:
                    analytics['score_distribution']['poor'] += 1
        
        return jsonify({
            'success': True,
            'analytics': analytics
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Analytics error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to load analytics'}), 500

@dashboard_bp.route('/activity', methods=['GET'])
@jwt_required()
def get_user_activity():
    """Get user activity feed"""
    try:
        current_user_id = get_jwt_identity()
        limit = int(request.args.get('limit', 20))
        
        activities = UserActivity.query.filter_by(user_id=current_user_id)\
                                      .order_by(UserActivity.created_at.desc())\
                                      .limit(limit).all()
        
        return jsonify({
            'success': True,
            'activities': [activity.to_dict() for activity in activities],
            'total': len(activities)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"User activity error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to load user activity'}), 500
