"""
Dr. Resume - Job Description Routes (US-04)
===========================================

This module contains job description-related routes including:
- Job description creation and management
- Text processing and keyword extraction
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user_models import User, db
from models.content_models import JobDescription
from models.dashboard_models import UserActivity

# Create blueprint
jd_bp = Blueprint('job_descriptions', __name__)

@jd_bp.route('/', methods=['POST'])
@jwt_required()
def create_job_description():
    """Create new job description"""
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user:
            return jsonify({'success': False, 'message': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['title', 'job_description_text']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field} is required'}), 400
        
        # Create job description
        jd = JobDescription.create_job_description(
            user_id=current_user_id,
            title=data['title'],
            job_description_text=data['job_description_text'],
            company_name=data.get('company_name'),
            location=data.get('location'),
            employment_type=data.get('employment_type', 'full-time'),
            experience_level=data.get('experience_level'),
            salary_range=data.get('salary_range'),
            job_url=data.get('job_url')
        )
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='jd_create',
            activity_title=f'Created job description: {jd.title}',
            activity_description=f'Job description created with ID: {jd.id}',
            job_description_id=jd.id
        )
        
        return jsonify({
            'success': True,
            'message': 'Job description created successfully',
            'job_description': jd.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Create job description error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to create job description'}), 500

@jd_bp.route('/', methods=['GET'])
@jwt_required()
def list_job_descriptions():
    """List user job descriptions"""
    try:
        current_user_id = get_jwt_identity()
        
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        include_content = request.args.get('include_content', 'false').lower() == 'true'
        
        job_descriptions = JobDescription.get_user_job_descriptions(current_user_id, active_only=active_only)
        
        return jsonify({
            'success': True,
            'job_descriptions': [jd.to_dict(include_content=include_content) for jd in job_descriptions],
            'total': len(job_descriptions)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"List job descriptions error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve job descriptions'}), 500

@jd_bp.route('/<jd_id>', methods=['GET'])
@jwt_required()
def get_job_description(jd_id):
    """Get specific job description"""
    try:
        current_user_id = get_jwt_identity()
        
        jd = JobDescription.query.filter_by(id=jd_id, user_id=current_user_id).first()
        if not jd:
            return jsonify({'success': False, 'message': 'Job description not found'}), 404
        
        include_content = request.args.get('include_content', 'true').lower() == 'true'
        
        return jsonify({
            'success': True,
            'job_description': jd.to_dict(include_content=include_content)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get job description error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve job description'}), 500

@jd_bp.route('/<jd_id>', methods=['PUT'])
@jwt_required()
def update_job_description(jd_id):
    """Update job description"""
    try:
        current_user_id = get_jwt_identity()
        
        jd = JobDescription.query.filter_by(id=jd_id, user_id=current_user_id).first()
        if not jd:
            return jsonify({'success': False, 'message': 'Job description not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        # Update fields
        updatable_fields = ['title', 'company_name', 'job_description_text', 'location', 
                           'employment_type', 'experience_level', 'salary_range', 'job_url']
        
        for field in updatable_fields:
            if field in data:
                setattr(jd, field, data[field])
        
        db.session.commit()
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='jd_update',
            activity_title=f'Updated job description: {jd.title}',
            activity_description=f'Job description updated for ID: {jd.id}',
            job_description_id=jd.id
        )
        
        return jsonify({
            'success': True,
            'message': 'Job description updated successfully',
            'job_description': jd.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update job description error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to update job description'}), 500

@jd_bp.route('/<jd_id>', methods=['DELETE'])
@jwt_required()
def delete_job_description(jd_id):
    """Delete job description"""
    try:
        current_user_id = get_jwt_identity()
        
        jd = JobDescription.query.filter_by(id=jd_id, user_id=current_user_id).first()
        if not jd:
            return jsonify({'success': False, 'message': 'Job description not found'}), 404
        
        # Soft delete
        jd.is_active = False
        db.session.commit()
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='jd_delete',
            activity_title=f'Deleted job description: {jd.title}',
            activity_description=f'Job description marked as inactive for ID: {jd.id}',
            job_description_id=jd.id
        )
        
        return jsonify({
            'success': True,
            'message': 'Job description deleted successfully'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete job description error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to delete job description'}), 500
