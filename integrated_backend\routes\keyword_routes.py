"""
Dr. Resume - Keyword Routes (US-05)
===================================

This module contains keyword extraction and analysis routes.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user_models import User
from models.analysis_models import Keyword

keyword_bp = Blueprint('keywords', __name__)

@keyword_bp.route('/document/<doc_type>/<doc_id>', methods=['GET'])
@jwt_required()
def get_document_keywords(doc_type, doc_id):
    """Get keywords for a document"""
    try:
        current_user_id = get_jwt_identity()
        keyword_type = request.args.get('keyword_type')
        
        if doc_type == 'resume':
            keywords = Keyword.get_document_keywords(current_user_id, resume_id=doc_id, keyword_type=keyword_type)
        elif doc_type == 'job_description':
            keywords = Keyword.get_document_keywords(current_user_id, job_description_id=doc_id, keyword_type=keyword_type)
        else:
            return jsonify({'success': False, 'message': 'Invalid document type'}), 400
        
        return jsonify({
            'success': True,
            'keywords': [kw.to_dict() for kw in keywords],
            'total': len(keywords)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get keywords error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve keywords'}), 500

@keyword_bp.route('/summary', methods=['GET'])
@jwt_required()
def get_keyword_summary():
    """Get keyword summary"""
    try:
        current_user_id = get_jwt_identity()
        resume_id = request.args.get('resume_id')
        job_description_id = request.args.get('job_description_id')
        
        summary = Keyword.get_keyword_summary(current_user_id, resume_id, job_description_id)
        
        return jsonify({
            'success': True,
            'summary': summary
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get keyword summary error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve keyword summary'}), 500
