"""
Dr. Resume - Matching Routes (US-06)
====================================

This module contains resume-job description matching routes.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user_models import User
from models.analysis_models import MatchingScore
from decimal import Decimal

matching_bp = Blueprint('matching', __name__)

@matching_bp.route('/calculate', methods=['POST'])
@jwt_required()
def calculate_matching():
    """Calculate matching score between resume and job description"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'resume_id' not in data or 'job_description_id' not in data:
            return jsonify({'success': False, 'message': 'Resume ID and Job Description ID required'}), 400
        
        resume_id = data['resume_id']
        job_description_id = data['job_description_id']
        
        # Simple matching calculation (in real app, this would be more sophisticated)
        overall_match = 75.5  # Mock calculation
        
        # Create matching score record
        matching_score = MatchingScore.create_matching_score(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            overall_match_percentage=overall_match,
            jaccard_similarity=0.65,
            keyword_overlap_count=15,
            resume_keyword_count=25,
            jd_keyword_count=20,
            skill_match_percentage=80.0,
            experience_match_percentage=70.0,
            education_match_percentage=85.0,
            technology_match_percentage=75.0,
            matched_keywords=['python', 'javascript', 'react', 'sql'],
            missing_keywords=['docker', 'kubernetes', 'aws'],
            extra_keywords=['php', 'wordpress']
        )
        
        return jsonify({
            'success': True,
            'message': 'Matching score calculated successfully',
            'matching_score': matching_score.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Calculate matching error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to calculate matching score'}), 500

@matching_bp.route('/<matching_id>', methods=['GET'])
@jwt_required()
def get_matching_score(matching_id):
    """Get specific matching score"""
    try:
        current_user_id = get_jwt_identity()
        
        matching_score = MatchingScore.query.filter_by(id=matching_id, user_id=current_user_id).first()
        if not matching_score:
            return jsonify({'success': False, 'message': 'Matching score not found'}), 404
        
        return jsonify({
            'success': True,
            'matching_score': matching_score.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get matching score error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve matching score'}), 500

@matching_bp.route('/', methods=['GET'])
@jwt_required()
def list_matching_scores():
    """List matching scores"""
    try:
        current_user_id = get_jwt_identity()
        resume_id = request.args.get('resume_id')
        job_description_id = request.args.get('job_description_id')
        
        matching_scores = MatchingScore.get_user_matching_scores(current_user_id, resume_id, job_description_id)
        
        return jsonify({
            'success': True,
            'matching_scores': [ms.to_dict() for ms in matching_scores],
            'total': len(matching_scores)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"List matching scores error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve matching scores'}), 500
