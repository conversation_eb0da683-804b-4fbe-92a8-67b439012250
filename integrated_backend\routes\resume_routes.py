"""
Dr. Resume - Resume Routes (US-03)
==================================

This module contains resume-related routes including:
- Resume upload and file processing
- Resume management (list, view, update, delete)
- File download and reprocessing
"""

import os
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from models.user_models import User, db
from models.content_models import Resume
from models.dashboard_models import UserActivity
from utils.file_processor import process_resume_file

# Create blueprint
resume_bp = Blueprint('resumes', __name__)

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_size(file):
    """Get file size in bytes"""
    file.seek(0, 2)  # Seek to end
    size = file.tell()
    file.seek(0)  # Reset to beginning
    return size

@resume_bp.route('/', methods=['POST'])
@jwt_required()
def upload_resume():
    """
    Resume Upload Endpoint (US-03)
    
    Handles file upload, validation, and initial processing.
    Stores file to local filesystem and creates database record.
    """
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'No file selected'
            }), 400
        
        # Validate file
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': 'Invalid file type. Only PDF, DOC, and DOCX files are allowed.'
            }), 400
        
        # Check file size
        file_size = get_file_size(file)
        if file_size > MAX_FILE_SIZE:
            return jsonify({
                'success': False,
                'message': f'File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB.'
            }), 400
        
        # Get additional form data
        resume_title = request.form.get('resume_title', '').strip()
        resume_description = request.form.get('resume_description', '').strip()
        
        # Generate secure filename
        original_filename = secure_filename(file.filename)
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        stored_filename = f"{uuid.uuid4()}.{file_extension}"
        
        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'resumes')
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save file
        file_path = os.path.join(upload_dir, stored_filename)
        file.save(file_path)
        
        # Create resume record
        resume = Resume.create_resume(
            user_id=current_user_id,
            original_filename=original_filename,
            stored_filename=stored_filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file.content_type or f'application/{file_extension}',
            file_extension=file_extension,
            resume_title=resume_title if resume_title else original_filename,
            resume_description=resume_description
        )
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='resume_upload',
            activity_title=f'Uploaded resume: {original_filename}',
            activity_description=f'Resume uploaded successfully with ID: {resume.id}',
            resume_id=resume.id,
            metadata={'file_size': file_size, 'file_type': file_extension}
        )
        
        # Start background processing (in a real app, this would be async)
        try:
            process_resume_file(resume.id)
        except Exception as e:
            current_app.logger.error(f"Resume processing error: {str(e)}")
            # Don't fail the upload if processing fails
        
        return jsonify({
            'success': True,
            'message': 'Resume uploaded successfully',
            'resume': resume.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Resume upload error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Resume upload failed. Please try again.'
        }), 500

@resume_bp.route('/', methods=['GET'])
@jwt_required()
def list_resumes():
    """
    List User Resumes Endpoint (US-03)
    
    Returns all resumes for the current user.
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        include_content = request.args.get('include_content', 'false').lower() == 'true'
        
        # Get user resumes
        resumes = Resume.get_user_resumes(current_user_id, active_only=active_only)
        
        return jsonify({
            'success': True,
            'resumes': [resume.to_dict(include_content=include_content) for resume in resumes],
            'total': len(resumes)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"List resumes error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve resumes'
        }), 500

@resume_bp.route('/<resume_id>', methods=['GET'])
@jwt_required()
def get_resume(resume_id):
    """
    Get Resume Details Endpoint (US-03)
    
    Returns detailed information about a specific resume.
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Get include_content parameter
        include_content = request.args.get('include_content', 'true').lower() == 'true'
        
        return jsonify({
            'success': True,
            'resume': resume.to_dict(include_content=include_content)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get resume error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve resume'
        }), 500

@resume_bp.route('/<resume_id>', methods=['PUT'])
@jwt_required()
def update_resume(resume_id):
    """
    Update Resume Endpoint (US-03)
    
    Updates resume metadata (title, description).
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update fields
        if 'resume_title' in data:
            resume.resume_title = data['resume_title'].strip()
        
        if 'resume_description' in data:
            resume.resume_description = data['resume_description'].strip()
        
        resume.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='resume_update',
            activity_title=f'Updated resume: {resume.resume_title}',
            activity_description=f'Resume metadata updated for ID: {resume.id}',
            resume_id=resume.id
        )
        
        return jsonify({
            'success': True,
            'message': 'Resume updated successfully',
            'resume': resume.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update resume error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to update resume'
        }), 500

@resume_bp.route('/<resume_id>', methods=['DELETE'])
@jwt_required()
def delete_resume(resume_id):
    """
    Delete Resume Endpoint (US-03)
    
    Soft deletes a resume (marks as inactive).
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Soft delete (mark as inactive)
        resume.is_active = False
        resume.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='resume_delete',
            activity_title=f'Deleted resume: {resume.resume_title}',
            activity_description=f'Resume marked as inactive for ID: {resume.id}',
            resume_id=resume.id
        )
        
        return jsonify({
            'success': True,
            'message': 'Resume deleted successfully'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete resume error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to delete resume'
        }), 500

@resume_bp.route('/<resume_id>/download', methods=['GET'])
@jwt_required()
def download_resume(resume_id):
    """
    Download Resume File Endpoint (US-03)
    
    Downloads the original resume file.
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Check if file exists
        if not os.path.exists(resume.file_path):
            return jsonify({
                'success': False,
                'message': 'Resume file not found on server'
            }), 404
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='resume_download',
            activity_title=f'Downloaded resume: {resume.resume_title}',
            activity_description=f'Resume file downloaded for ID: {resume.id}',
            resume_id=resume.id
        )
        
        return send_file(
            resume.file_path,
            as_attachment=True,
            download_name=resume.original_filename,
            mimetype=resume.file_type
        )
        
    except Exception as e:
        current_app.logger.error(f"Download resume error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to download resume'
        }), 500

@resume_bp.route('/<resume_id>/reprocess', methods=['POST'])
@jwt_required()
def reprocess_resume(resume_id):
    """
    Reprocess Resume Endpoint (US-03)
    
    Reprocesses a resume file (re-extracts text and keywords).
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Check if file exists
        if not os.path.exists(resume.file_path):
            return jsonify({
                'success': False,
                'message': 'Resume file not found on server'
            }), 404
        
        # Reset processing status
        resume.extraction_status = 'pending'
        resume.upload_status = 'processing'
        resume.extracted_text = None
        resume.extraction_error = None
        resume.processing_start_time = datetime.utcnow()
        resume.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Start reprocessing
        try:
            process_resume_file(resume.id)
        except Exception as e:
            current_app.logger.error(f"Resume reprocessing error: {str(e)}")
            resume.extraction_status = 'failed'
            resume.extraction_error = str(e)
            db.session.commit()
            
            return jsonify({
                'success': False,
                'message': 'Resume reprocessing failed'
            }), 500
        
        # Log activity
        UserActivity.log_activity(
            user_id=current_user_id,
            activity_type='resume_reprocess',
            activity_title=f'Reprocessed resume: {resume.resume_title}',
            activity_description=f'Resume reprocessing initiated for ID: {resume.id}',
            resume_id=resume.id
        )
        
        return jsonify({
            'success': True,
            'message': 'Resume reprocessing started',
            'resume': resume.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Reprocess resume error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to reprocess resume'
        }), 500
