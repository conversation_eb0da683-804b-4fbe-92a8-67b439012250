"""
Dr. Resume - Suggestion Routes (US-07)
======================================

This module contains suggestion generation routes.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user_models import User
from models.suggestion_models import Suggestion, PremiumSuggestion, SuggestionType, SuggestionPriority

suggestion_bp = Blueprint('suggestions', __name__)

@suggestion_bp.route('/basic', methods=['POST'])
@jwt_required()
def generate_basic_suggestions():
    """Generate basic suggestions"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'resume_id' not in data or 'job_description_id' not in data:
            return jsonify({'success': False, 'message': 'Resume ID and Job Description ID required'}), 400
        
        resume_id = data['resume_id']
        job_description_id = data['job_description_id']
        
        # Generate sample basic suggestions
        suggestions = []
        
        # Sample suggestion 1
        suggestion1 = Suggestion.create_suggestion(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            suggestion_type=SuggestionType.KEYWORD_MISSING,
            title="Add Missing Keywords",
            description="Your resume is missing some important keywords from the job description.",
            priority=SuggestionPriority.HIGH,
            missing_keywords=['docker', 'kubernetes', 'aws'],
            suggested_keywords=['containerization', 'cloud computing', 'devops'],
            confidence_score=0.85,
            implementation_difficulty=2,
            expected_impact=15.0
        )
        suggestions.append(suggestion1)
        
        # Sample suggestion 2
        suggestion2 = Suggestion.create_suggestion(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            suggestion_type=SuggestionType.SKILL_ENHANCEMENT,
            title="Highlight Technical Skills",
            description="Consider emphasizing your technical skills more prominently.",
            priority=SuggestionPriority.MEDIUM,
            confidence_score=0.75,
            implementation_difficulty=3,
            expected_impact=10.0
        )
        suggestions.append(suggestion2)
        
        return jsonify({
            'success': True,
            'message': 'Basic suggestions generated successfully',
            'suggestions': [s.to_dict() for s in suggestions],
            'total': len(suggestions)
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Generate basic suggestions error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to generate suggestions'}), 500

@suggestion_bp.route('/premium', methods=['POST'])
@jwt_required()
def generate_premium_suggestions():
    """Generate premium AI suggestions"""
    try:
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user or not user.is_premium:
            return jsonify({'success': False, 'message': 'Premium subscription required'}), 403
        
        data = request.get_json()
        
        if not data or 'resume_id' not in data or 'job_description_id' not in data:
            return jsonify({'success': False, 'message': 'Resume ID and Job Description ID required'}), 400
        
        resume_id = data['resume_id']
        job_description_id = data['job_description_id']
        
        # Generate sample premium suggestion
        premium_suggestion = PremiumSuggestion.create_premium_suggestion(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            suggestion_type=SuggestionType.CONTENT_OPTIMIZATION,
            title="AI-Powered Resume Optimization",
            description="Based on AI analysis, here are personalized recommendations to improve your resume.",
            ai_analysis="Your resume shows strong technical skills but could benefit from more quantified achievements. The job description emphasizes leadership and project management, which you should highlight more prominently.",
            priority=SuggestionPriority.HIGH,
            detailed_recommendations=[
                "Add specific metrics to your achievements (e.g., 'Improved system performance by 40%')",
                "Include leadership examples from your project experience",
                "Emphasize cloud technologies mentioned in the job description"
            ],
            personalized_tips=[
                "Your Python experience is excellent - make it more prominent",
                "Consider adding a brief summary section at the top",
                "Use action verbs like 'implemented', 'optimized', 'led'"
            ],
            industry_insights={
                "market_trend": "Cloud technologies are in high demand",
                "salary_impact": "Adding AWS certification could increase salary by 15-20%",
                "skill_demand": "DevOps skills are highly valued in this role"
            },
            market_relevance_score=0.92,
            career_progression_impact=0.85,
            salary_impact_estimate=15000.0,
            ai_model_used='gpt-3.5-turbo',
            ai_response_tokens=250,
            ai_cost_estimate=0.0005
        )
        
        return jsonify({
            'success': True,
            'message': 'Premium suggestions generated successfully',
            'suggestion': premium_suggestion.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Generate premium suggestions error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to generate premium suggestions'}), 500

@suggestion_bp.route('/', methods=['GET'])
@jwt_required()
def list_suggestions():
    """List suggestions"""
    try:
        current_user_id = get_jwt_identity()
        resume_id = request.args.get('resume_id')
        job_description_id = request.args.get('job_description_id')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        # Get basic suggestions
        basic_suggestions = Suggestion.get_user_suggestions(current_user_id, resume_id, job_description_id, active_only)
        
        # Get premium suggestions
        premium_suggestions = PremiumSuggestion.get_user_premium_suggestions(current_user_id, resume_id, job_description_id, active_only)
        
        return jsonify({
            'success': True,
            'basic_suggestions': [s.to_dict() for s in basic_suggestions],
            'premium_suggestions': [s.to_dict() for s in premium_suggestions],
            'total_basic': len(basic_suggestions),
            'total_premium': len(premium_suggestions)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"List suggestions error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to retrieve suggestions'}), 500

@suggestion_bp.route('/<suggestion_id>/implement', methods=['POST'])
@jwt_required()
def implement_suggestion(suggestion_id):
    """Mark suggestion as implemented"""
    try:
        current_user_id = get_jwt_identity()
        
        # Try basic suggestion first
        suggestion = Suggestion.query.filter_by(id=suggestion_id, user_id=current_user_id).first()
        if not suggestion:
            # Try premium suggestion
            suggestion = PremiumSuggestion.query.filter_by(id=suggestion_id, user_id=current_user_id).first()
        
        if not suggestion:
            return jsonify({'success': False, 'message': 'Suggestion not found'}), 404
        
        suggestion.is_implemented = True
        from models.user_models import db
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Suggestion marked as implemented'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Implement suggestion error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to implement suggestion'}), 500

@suggestion_bp.route('/<suggestion_id>/dismiss', methods=['POST'])
@jwt_required()
def dismiss_suggestion(suggestion_id):
    """Dismiss suggestion"""
    try:
        current_user_id = get_jwt_identity()
        
        # Try basic suggestion first
        suggestion = Suggestion.query.filter_by(id=suggestion_id, user_id=current_user_id).first()
        if not suggestion:
            # Try premium suggestion
            suggestion = PremiumSuggestion.query.filter_by(id=suggestion_id, user_id=current_user_id).first()
        
        if not suggestion:
            return jsonify({'success': False, 'message': 'Suggestion not found'}), 404
        
        suggestion.is_dismissed = True
        from models.user_models import db
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Suggestion dismissed'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Dismiss suggestion error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to dismiss suggestion'}), 500
