#!/usr/bin/env python3
"""
Dr. Resume - Integrated Backend Startup Script
==============================================

This script starts the integrated Flask backend server with all US-01 to US-10 features.
It handles environment setup, database initialization, and server startup.
"""

import os
import sys
import logging
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if required packages are installed"""
    required_packages = [
        'flask', 'flask_sqlalchemy', 'flask_jwt_extended', 
        'flask_cors', 'flask_limiter', 'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error("Missing required packages:")
        for package in missing_packages:
            logger.error(f"  - {package}")
        logger.error("\nPlease install requirements:")
        logger.error("  pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """Set up environment variables"""
    from dotenv import load_dotenv
    
    # Load environment variables from .env file
    env_file = current_dir / '.env'
    if env_file.exists():
        load_dotenv(env_file)
        logger.info("✅ Environment variables loaded from .env file")
    else:
        logger.warning("⚠️  No .env file found. Using default configuration.")
        logger.info("💡 Copy .env.example to .env and configure your settings")
    
    # Set default values if not provided
    os.environ.setdefault('FLASK_ENV', 'development')
    os.environ.setdefault('FLASK_DEBUG', 'True')
    os.environ.setdefault('SECRET_KEY', 'dev-secret-key-change-in-production')
    os.environ.setdefault('JWT_SECRET_KEY', 'dev-jwt-secret-change-in-production')

def check_database():
    """Check database connectivity and create tables if needed"""
    try:
        from app import create_app, db
        
        app = create_app('development')
        with app.app_context():
            # Test database connection
            db.engine.execute('SELECT 1')
            logger.info("✅ Database connection successful")
            
            # Create tables if they don't exist
            db.create_all()
            logger.info("✅ Database tables verified/created")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Database error: {str(e)}")
        return False

def check_directories():
    """Create necessary directories"""
    directories = [
        'uploads',
        'uploads/resumes',
        'logs'
    ]
    
    for directory in directories:
        dir_path = current_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Directory ensured: {directory}")

def print_startup_info():
    """Print startup information"""
    print("\n" + "="*60)
    print("🚀 Dr. Resume - Integrated Backend Server")
    print("="*60)
    print("📋 Features: All US-01 to US-10 integrated")
    print("🌐 Server: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000")
    print("🔍 Health Check: http://localhost:8000/health")
    print("\n📋 Available API Endpoints:")
    print("   🔐 Authentication: /api/auth/*")
    print("   📄 Resumes: /api/resumes/*")
    print("   💼 Job Descriptions: /api/job-descriptions/*")
    print("   🔑 Keywords: /api/keywords/*")
    print("   📊 Matching: /api/matching/*")
    print("   💡 Suggestions: /api/suggestions/*")
    print("   📈 Dashboard: /api/dashboard/*")
    print("   🔒 Security: /api/security/*")
    print("   ⚙️  Account: /api/account/*")
    print("\n💾 Database: SQLite (development)")
    print("🔧 Configuration: Check .env file for settings")
    print("📖 Documentation: See integrated_README.md")
    print("="*60)

def main():
    """Main startup function"""
    try:
        print("🔍 Checking system requirements...")
        
        # Check if required packages are installed
        if not check_requirements():
            sys.exit(1)
        
        # Set up environment
        setup_environment()
        
        # Check and create directories
        check_directories()
        
        # Check database
        if not check_database():
            logger.error("❌ Database setup failed. Please check your configuration.")
            sys.exit(1)
        
        # Print startup information
        print_startup_info()
        
        # Import and run the app
        from app import run_development_server
        
        print("\n🚀 Starting server...")
        print("Press Ctrl+C to stop the server\n")
        
        # Start the development server
        run_development_server()
        
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Startup failed: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
