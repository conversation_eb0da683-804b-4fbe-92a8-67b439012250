"""
Dr. Resume - Simple Test Server
==============================

A minimal working version to test the basic functionality.
"""

import os
import uuid
from datetime import datetime, timedelta
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash

# Create Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'dev-secret-key'
app.config['JWT_SECRET_KEY'] = 'jwt-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///dr_resume_test.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# Simple User model
class User(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    is_premium = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_premium': self.is_premium,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# Simple Resume model
class Resume(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# Simple JobDescription model
class JobDescription(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    company = db.Column(db.String(100))
    description = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'company': self.company,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# Routes
@app.route('/')
def home():
    return jsonify({
        'success': True,
        'message': 'Dr. Resume API - Simple Test Version',
        'version': '1.0.0',
        'endpoints': {
            'auth': '/api/auth/*',
            'resumes': '/api/resumes/*',
            'job_descriptions': '/api/job-descriptions/*'
        }
    })

@app.route('/health')
def health():
    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat()
    })

# Auth routes
@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        if not data or not data.get('email') or not data.get('password'):
            return jsonify({'success': False, 'message': 'Email and password required'}), 400
        
        # Check if user exists
        if User.query.filter_by(email=data['email']).first():
            return jsonify({'success': False, 'message': 'Email already registered'}), 409
        
        # Create user
        user = User(
            email=data['email'],
            first_name=data.get('first_name'),
            last_name=data.get('last_name')
        )
        user.set_password(data['password'])
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        if not data or not data.get('email') or not data.get('password'):
            return jsonify({'success': False, 'message': 'Email and password required'}), 400
        
        user = User.query.filter_by(email=data['email']).first()
        
        if not user or not user.check_password(data['password']):
            return jsonify({'success': False, 'message': 'Invalid credentials'}), 401
        
        access_token = create_access_token(identity=user.id)
        
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': user.to_dict(),
            'tokens': {
                'access_token': access_token,
                'token_type': 'Bearer'
            }
        }), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'success': False, 'message': 'User not found'}), 404
        
        return jsonify({
            'success': True,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Resume routes
@app.route('/api/resumes', methods=['GET'])
@jwt_required()
def get_resumes():
    try:
        user_id = get_jwt_identity()
        resumes = Resume.query.filter_by(user_id=user_id).all()
        
        return jsonify({
            'success': True,
            'resumes': [r.to_dict() for r in resumes],
            'total': len(resumes)
        }), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/resumes', methods=['POST'])
@jwt_required()
def create_resume():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('title'):
            return jsonify({'success': False, 'message': 'Title required'}), 400
        
        resume = Resume(
            user_id=user_id,
            title=data['title'],
            content=data.get('content', '')
        )
        
        db.session.add(resume)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Resume created successfully',
            'resume': resume.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Job Description routes
@app.route('/api/job-descriptions', methods=['GET'])
@jwt_required()
def get_job_descriptions():
    try:
        user_id = get_jwt_identity()
        jds = JobDescription.query.filter_by(user_id=user_id).all()
        
        return jsonify({
            'success': True,
            'job_descriptions': [jd.to_dict() for jd in jds],
            'total': len(jds)
        }), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/job-descriptions', methods=['POST'])
@jwt_required()
def create_job_description():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('title') or not data.get('description'):
            return jsonify({'success': False, 'message': 'Title and description required'}), 400
        
        jd = JobDescription(
            user_id=user_id,
            title=data['title'],
            company=data.get('company'),
            description=data['description']
        )
        
        db.session.add(jd)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Job description created successfully',
            'job_description': jd.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# Dashboard route
@app.route('/api/dashboard/overview', methods=['GET'])
@jwt_required()
def dashboard_overview():
    try:
        user_id = get_jwt_identity()
        
        total_resumes = Resume.query.filter_by(user_id=user_id).count()
        total_jds = JobDescription.query.filter_by(user_id=user_id).count()
        
        return jsonify({
            'success': True,
            'overview': {
                'total_resumes': total_resumes,
                'total_job_descriptions': total_jds,
                'total_scans': 0,
                'monthly_scans': 0,
                'average_match_score': 0.0,
                'recent_activity': []
            }
        }), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("🚀 Starting Dr. Resume Simple Test Server...")
        print("🌐 Server: http://localhost:8000")
        print("📚 API Docs: http://localhost:8000")
        print("🔍 Health: http://localhost:8000/health")
    
    app.run(host='0.0.0.0', port=8000, debug=True)
