"""
Dr. Resume - File Processing Utilities
=====================================

This module contains utilities for processing uploaded files including:
- Text extraction from PDF and DOC files
- Keyword extraction using NLP
- File validation and security checks
"""

import os
import re
import logging
from datetime import datetime
from typing import Optional, Dict, List, Tuple

# File processing libraries
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import spacy
    # Try to load English model
    try:
        nlp = spacy.load("en_core_web_sm")
        SPACY_AVAILABLE = True
    except OSError:
        SPACY_AVAILABLE = False
        nlp = None
except ImportError:
    SPACY_AVAILABLE = False
    nlp = None

# Fallback to NLTK if spaCy is not available
try:
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    from nltk.tag import pos_tag
    NLTK_AVAILABLE = True
    
    # Download required NLTK data if not present
    try:
        nltk.data.find('tokenizers/punkt')
        nltk.data.find('corpora/stopwords')
        nltk.data.find('taggers/averaged_perceptron_tagger')
    except LookupError:
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        nltk.download('averaged_perceptron_tagger', quiet=True)
        
except ImportError:
    NLTK_AVAILABLE = False

logger = logging.getLogger(__name__)

def extract_text_from_pdf(file_path: str) -> Tuple[bool, str, Optional[str]]:
    """
    Extract text from PDF file
    
    Args:
        file_path (str): Path to PDF file
        
    Returns:
        Tuple[bool, str, Optional[str]]: (success, extracted_text, error_message)
    """
    if not PDF_AVAILABLE:
        return False, "", "PyPDF2 library not available"
    
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
            
            # Clean up text
            text = re.sub(r'\s+', ' ', text).strip()
            
            if not text:
                return False, "", "No text could be extracted from PDF"
            
            return True, text, None
            
    except Exception as e:
        logger.error(f"PDF extraction error: {str(e)}")
        return False, "", f"PDF extraction failed: {str(e)}"

def extract_text_from_docx(file_path: str) -> Tuple[bool, str, Optional[str]]:
    """
    Extract text from DOCX file
    
    Args:
        file_path (str): Path to DOCX file
        
    Returns:
        Tuple[bool, str, Optional[str]]: (success, extracted_text, error_message)
    """
    if not DOCX_AVAILABLE:
        return False, "", "python-docx library not available"
    
    try:
        doc = docx.Document(file_path)
        text = ""
        
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        
        # Also extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text += cell.text + " "
                text += "\n"
        
        # Clean up text
        text = re.sub(r'\s+', ' ', text).strip()
        
        if not text:
            return False, "", "No text could be extracted from DOCX"
        
        return True, text, None
        
    except Exception as e:
        logger.error(f"DOCX extraction error: {str(e)}")
        return False, "", f"DOCX extraction failed: {str(e)}"

def extract_text_from_doc(file_path: str) -> Tuple[bool, str, Optional[str]]:
    """
    Extract text from DOC file (legacy format)
    
    Note: This is a simplified implementation. For production use,
    consider using libraries like python-docx2txt or antiword.
    
    Args:
        file_path (str): Path to DOC file
        
    Returns:
        Tuple[bool, str, Optional[str]]: (success, extracted_text, error_message)
    """
    try:
        # For now, return a message indicating manual processing needed
        return False, "", "DOC file format requires manual text extraction"
        
    except Exception as e:
        logger.error(f"DOC extraction error: {str(e)}")
        return False, "", f"DOC extraction failed: {str(e)}"

def extract_text_from_file(file_path: str, file_extension: str) -> Tuple[bool, str, Optional[str]]:
    """
    Extract text from file based on extension
    
    Args:
        file_path (str): Path to file
        file_extension (str): File extension (pdf, docx, doc)
        
    Returns:
        Tuple[bool, str, Optional[str]]: (success, extracted_text, error_message)
    """
    if not os.path.exists(file_path):
        return False, "", "File not found"
    
    file_extension = file_extension.lower()
    
    if file_extension == 'pdf':
        return extract_text_from_pdf(file_path)
    elif file_extension == 'docx':
        return extract_text_from_docx(file_path)
    elif file_extension == 'doc':
        return extract_text_from_doc(file_path)
    else:
        return False, "", f"Unsupported file extension: {file_extension}"

def extract_keywords_spacy(text: str) -> List[Dict[str, any]]:
    """
    Extract keywords using spaCy NLP
    
    Args:
        text (str): Text to analyze
        
    Returns:
        List[Dict]: List of keyword dictionaries
    """
    if not SPACY_AVAILABLE or not nlp:
        return []
    
    try:
        doc = nlp(text)
        keywords = []
        
        # Extract named entities
        for ent in doc.ents:
            if ent.label_ in ['PERSON', 'ORG', 'GPE', 'PRODUCT', 'EVENT']:
                keywords.append({
                    'keyword': ent.text.lower().strip(),
                    'keyword_type': 'entity',
                    'confidence_score': 0.9,
                    'context_snippet': ent.sent.text[:200] if ent.sent else '',
                    'position_in_document': ent.start_char
                })
        
        # Extract skills and technologies (noun phrases)
        for chunk in doc.noun_chunks:
            if len(chunk.text.split()) <= 3:  # Keep short phrases
                # Filter out common words
                if not any(token.is_stop or token.is_punct for token in chunk):
                    keywords.append({
                        'keyword': chunk.text.lower().strip(),
                        'keyword_type': 'skill',
                        'confidence_score': 0.7,
                        'context_snippet': chunk.sent.text[:200] if chunk.sent else '',
                        'position_in_document': chunk.start_char
                    })
        
        # Extract important single tokens
        for token in doc:
            if (token.pos_ in ['NOUN', 'PROPN'] and 
                not token.is_stop and 
                not token.is_punct and 
                len(token.text) > 2):
                
                keywords.append({
                    'keyword': token.text.lower().strip(),
                    'keyword_type': 'technology' if token.pos_ == 'PROPN' else 'skill',
                    'confidence_score': 0.6,
                    'context_snippet': token.sent.text[:200] if token.sent else '',
                    'position_in_document': token.idx
                })
        
        # Remove duplicates and sort by confidence
        unique_keywords = {}
        for kw in keywords:
            key = kw['keyword']
            if key not in unique_keywords or kw['confidence_score'] > unique_keywords[key]['confidence_score']:
                unique_keywords[key] = kw
        
        return list(unique_keywords.values())
        
    except Exception as e:
        logger.error(f"spaCy keyword extraction error: {str(e)}")
        return []

def extract_keywords_nltk(text: str) -> List[Dict[str, any]]:
    """
    Extract keywords using NLTK (fallback method)
    
    Args:
        text (str): Text to analyze
        
    Returns:
        List[Dict]: List of keyword dictionaries
    """
    if not NLTK_AVAILABLE:
        return []
    
    try:
        # Tokenize and tag parts of speech
        tokens = word_tokenize(text.lower())
        pos_tags = pos_tag(tokens)
        
        # Get English stopwords
        stop_words = set(stopwords.words('english'))
        
        keywords = []
        
        # Extract nouns and proper nouns
        for i, (word, pos) in enumerate(pos_tags):
            if (pos in ['NN', 'NNS', 'NNP', 'NNPS'] and 
                word not in stop_words and 
                len(word) > 2 and 
                word.isalpha()):
                
                keyword_type = 'technology' if pos in ['NNP', 'NNPS'] else 'skill'
                
                keywords.append({
                    'keyword': word,
                    'keyword_type': keyword_type,
                    'confidence_score': 0.6,
                    'context_snippet': ' '.join(tokens[max(0, i-5):i+6]),
                    'position_in_document': i
                })
        
        # Remove duplicates
        unique_keywords = {}
        for kw in keywords:
            key = kw['keyword']
            if key not in unique_keywords:
                unique_keywords[key] = kw
        
        return list(unique_keywords.values())
        
    except Exception as e:
        logger.error(f"NLTK keyword extraction error: {str(e)}")
        return []

def extract_keywords_basic(text: str) -> List[Dict[str, any]]:
    """
    Basic keyword extraction using regex patterns (fallback method)
    
    Args:
        text (str): Text to analyze
        
    Returns:
        List[Dict]: List of keyword dictionaries
    """
    try:
        keywords = []
        
        # Common technology patterns
        tech_patterns = [
            r'\b(python|java|javascript|react|angular|vue|node\.?js|php|ruby|go|rust|swift|kotlin)\b',
            r'\b(html|css|sql|mongodb|postgresql|mysql|redis|docker|kubernetes|aws|azure|gcp)\b',
            r'\b(git|github|gitlab|jenkins|ci/cd|devops|agile|scrum|kanban)\b',
            r'\b(machine learning|ai|artificial intelligence|data science|analytics)\b'
        ]
        
        for pattern in tech_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                keywords.append({
                    'keyword': match.group().lower(),
                    'keyword_type': 'technology',
                    'confidence_score': 0.8,
                    'context_snippet': text[max(0, match.start()-50):match.end()+50],
                    'position_in_document': match.start()
                })
        
        # Experience patterns
        exp_patterns = [
            r'\b(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)\b',
            r'\b(senior|junior|lead|principal|architect|manager|director)\b'
        ]
        
        for pattern in exp_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                keywords.append({
                    'keyword': match.group().lower(),
                    'keyword_type': 'experience',
                    'confidence_score': 0.7,
                    'context_snippet': text[max(0, match.start()-50):match.end()+50],
                    'position_in_document': match.start()
                })
        
        # Education patterns
        edu_patterns = [
            r'\b(bachelor|master|phd|doctorate|degree|diploma|certificate)\b',
            r'\b(computer science|engineering|mathematics|physics|business)\b'
        ]
        
        for pattern in edu_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                keywords.append({
                    'keyword': match.group().lower(),
                    'keyword_type': 'education',
                    'confidence_score': 0.6,
                    'context_snippet': text[max(0, match.start()-50):match.end()+50],
                    'position_in_document': match.start()
                })
        
        # Remove duplicates
        unique_keywords = {}
        for kw in keywords:
            key = kw['keyword']
            if key not in unique_keywords or kw['confidence_score'] > unique_keywords[key]['confidence_score']:
                unique_keywords[key] = kw
        
        return list(unique_keywords.values())
        
    except Exception as e:
        logger.error(f"Basic keyword extraction error: {str(e)}")
        return []

def extract_keywords_from_text(text: str) -> List[Dict[str, any]]:
    """
    Extract keywords from text using available NLP libraries
    
    Args:
        text (str): Text to analyze
        
    Returns:
        List[Dict]: List of keyword dictionaries
    """
    if not text or not text.strip():
        return []
    
    # Try spaCy first (most accurate)
    if SPACY_AVAILABLE:
        keywords = extract_keywords_spacy(text)
        if keywords:
            return keywords
    
    # Fallback to NLTK
    if NLTK_AVAILABLE:
        keywords = extract_keywords_nltk(text)
        if keywords:
            return keywords
    
    # Final fallback to basic regex extraction
    return extract_keywords_basic(text)

def process_resume_file(resume_id: str) -> bool:
    """
    Process a resume file: extract text and keywords
    
    Args:
        resume_id (str): Resume ID to process
        
    Returns:
        bool: True if processing was successful
    """
    try:
        # Import here to avoid circular imports
        from models.content_models import Resume, db
        from models.analysis_models import Keyword
        
        # Get resume record
        resume = Resume.query.filter_by(id=resume_id).first()
        if not resume:
            logger.error(f"Resume not found: {resume_id}")
            return False
        
        # Update processing status
        resume.processing_start_time = datetime.utcnow()
        resume.upload_status = 'processing'
        resume.extraction_status = 'pending'
        db.session.commit()
        
        # Extract text from file
        success, extracted_text, error = extract_text_from_file(
            resume.file_path, 
            resume.file_extension
        )
        
        if not success:
            resume.extraction_status = 'failed'
            resume.extraction_error = error
            resume.processing_end_time = datetime.utcnow()
            db.session.commit()
            return False
        
        # Save extracted text
        resume.extracted_text = extracted_text
        resume.extraction_status = 'success'
        
        # Extract keywords
        keywords = extract_keywords_from_text(extracted_text)
        
        # Save keywords to database
        for kw_data in keywords:
            keyword = Keyword(
                user_id=resume.user_id,
                resume_id=resume.id,
                keyword=kw_data['keyword'],
                keyword_type=kw_data['keyword_type'],
                confidence_score=kw_data['confidence_score'],
                context_snippet=kw_data.get('context_snippet'),
                position_in_document=kw_data.get('position_in_document'),
                extraction_method='spacy' if SPACY_AVAILABLE else 'nltk' if NLTK_AVAILABLE else 'basic'
            )
            db.session.add(keyword)
        
        # Update processing completion
        resume.upload_status = 'processed'
        resume.processing_end_time = datetime.utcnow()
        
        if resume.processing_start_time and resume.processing_end_time:
            duration = (resume.processing_end_time - resume.processing_start_time).total_seconds()
            resume.processing_duration_seconds = int(duration)
        
        db.session.commit()
        
        logger.info(f"Resume processed successfully: {resume_id}, extracted {len(keywords)} keywords")
        return True
        
    except Exception as e:
        logger.error(f"Resume processing error for {resume_id}: {str(e)}")
        
        # Update error status
        try:
            from models.content_models import Resume, db
            resume = Resume.query.filter_by(id=resume_id).first()
            if resume:
                resume.extraction_status = 'failed'
                resume.extraction_error = str(e)
                resume.processing_end_time = datetime.utcnow()
                db.session.commit()
        except:
            pass
        
        return False
