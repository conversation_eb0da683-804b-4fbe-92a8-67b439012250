"""
Dr. Resume - Sample Data Generator
=================================

This module creates realistic sample data for testing and demonstration purposes.
It populates the database with users, resumes, job descriptions, and related data.
"""

import os
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from models.user_models import User, UserProfile, UserPreferences, db
from models.content_models import Resume, JobDescription
from models.analysis_models import Keyword, MatchingScore
from models.suggestion_models import Suggestion, PremiumSuggestion, SuggestionType, SuggestionPriority
from models.dashboard_models import ScanHistory, DashboardAnalytics, UserActivity
from models.subscription_models import SubscriptionPlan, UserSubscription

def create_subscription_plans():
    """Create default subscription plans"""
    plans = [
        {
            'name': 'free',
            'display_name': 'Free Plan',
            'description': 'Basic resume analysis with limited features',
            'price_monthly': 0.0,
            'price_yearly': 0.0,
            'max_resumes': 3,
            'max_job_descriptions': 5,
            'max_scans_per_month': 10,
            'has_premium_suggestions': False,
            'has_ai_analysis': False,
            'has_advanced_analytics': False,
            'has_export_features': False,
            'has_priority_support': False,
            'ai_tokens_per_month': 0,
            'max_premium_suggestions_per_month': 0,
            'features_list': [
                'Basic resume analysis',
                'Job description matching',
                'Basic keyword extraction',
                'Simple suggestions',
                'Limited scans per month'
            ],
            'is_active': True,
            'is_popular': False,
            'sort_order': 1
        },
        {
            'name': 'premium',
            'display_name': 'Premium Plan',
            'description': 'Advanced AI-powered analysis with unlimited features',
            'price_monthly': 19.99,
            'price_yearly': 199.99,
            'max_resumes': -1,  # Unlimited
            'max_job_descriptions': -1,  # Unlimited
            'max_scans_per_month': -1,  # Unlimited
            'has_premium_suggestions': True,
            'has_ai_analysis': True,
            'has_advanced_analytics': True,
            'has_export_features': True,
            'has_priority_support': True,
            'ai_tokens_per_month': 100000,
            'max_premium_suggestions_per_month': 50,
            'features_list': [
                'Unlimited resume analysis',
                'AI-powered suggestions',
                'Advanced analytics dashboard',
                'Export to PDF/Excel',
                'Priority email support',
                'Industry insights',
                'Salary impact estimates'
            ],
            'is_active': True,
            'is_popular': True,
            'sort_order': 2
        },
        {
            'name': 'enterprise',
            'display_name': 'Enterprise Plan',
            'description': 'Full-featured plan for teams and organizations',
            'price_monthly': 49.99,
            'price_yearly': 499.99,
            'max_resumes': -1,  # Unlimited
            'max_job_descriptions': -1,  # Unlimited
            'max_scans_per_month': -1,  # Unlimited
            'has_premium_suggestions': True,
            'has_ai_analysis': True,
            'has_advanced_analytics': True,
            'has_export_features': True,
            'has_priority_support': True,
            'ai_tokens_per_month': -1,  # Unlimited
            'max_premium_suggestions_per_month': -1,  # Unlimited
            'features_list': [
                'Everything in Premium',
                'Unlimited AI tokens',
                'Team collaboration',
                'Custom integrations',
                'Dedicated support',
                'Advanced reporting',
                'API access'
            ],
            'is_active': True,
            'is_popular': False,
            'sort_order': 3
        }
    ]
    
    for plan_data in plans:
        existing_plan = SubscriptionPlan.query.filter_by(name=plan_data['name']).first()
        if not existing_plan:
            plan = SubscriptionPlan(**plan_data)
            db.session.add(plan)
    
    db.session.commit()

def create_sample_users():
    """Create sample users with profiles and preferences"""
    users_data = [
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'John',
            'last_name': 'Doe',
            'is_premium': True,
            'profile': {
                'phone_number': '******-0123',
                'location': 'San Francisco, CA',
                'current_job_title': 'Senior Software Engineer',
                'current_company': 'Tech Corp',
                'industry': 'Technology',
                'experience_years': 8,
                'bio': 'Experienced software engineer with expertise in full-stack development and cloud technologies.'
            }
        },
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'is_premium': False,
            'profile': {
                'phone_number': '******-0456',
                'location': 'New York, NY',
                'current_job_title': 'Product Manager',
                'current_company': 'StartupXYZ',
                'industry': 'Technology',
                'experience_years': 5,
                'bio': 'Product manager passionate about user experience and data-driven decision making.'
            }
        },
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'Mike',
            'last_name': 'Johnson',
            'is_premium': True,
            'profile': {
                'phone_number': '******-0789',
                'location': 'Austin, TX',
                'current_job_title': 'Data Scientist',
                'current_company': 'Analytics Inc',
                'industry': 'Data Science',
                'experience_years': 6,
                'bio': 'Data scientist specializing in machine learning and predictive analytics.'
            }
        }
    ]
    
    created_users = []
    
    for user_data in users_data:
        existing_user = User.find_by_email(user_data['email'])
        if not existing_user:
            # Create user
            profile_data = user_data.pop('profile')
            user = User(**user_data)
            db.session.add(user)
            db.session.flush()  # Get user ID
            
            # Create profile
            profile = UserProfile(user_id=user.id, **profile_data)
            profile.calculate_completion_percentage()
            db.session.add(profile)
            
            # Create preferences
            preferences = UserPreferences(user_id=user.id)
            db.session.add(preferences)
            
            # Create subscription (free plan for non-premium users)
            free_plan = SubscriptionPlan.query.filter_by(name='free').first()
            premium_plan = SubscriptionPlan.query.filter_by(name='premium').first()
            
            if user.is_premium and premium_plan:
                subscription = UserSubscription(
                    user_id=user.id,
                    subscription_plan_id=premium_plan.id,
                    billing_cycle='monthly',
                    status='active'
                )
            elif free_plan:
                subscription = UserSubscription(
                    user_id=user.id,
                    subscription_plan_id=free_plan.id,
                    billing_cycle='monthly',
                    status='active'
                )
            else:
                subscription = None
            
            if subscription:
                db.session.add(subscription)
            
            created_users.append(user)
    
    db.session.commit()
    return created_users

def create_sample_job_descriptions(users):
    """Create sample job descriptions"""
    jd_data = [
        {
            'title': 'Senior Full Stack Developer',
            'company_name': 'Tech Innovations Inc',
            'job_description_text': '''
We are seeking a Senior Full Stack Developer to join our dynamic team. The ideal candidate will have extensive experience in both front-end and back-end development.

Key Responsibilities:
- Develop and maintain web applications using React, Node.js, and Python
- Design and implement RESTful APIs
- Work with databases including PostgreSQL and MongoDB
- Collaborate with cross-functional teams using Agile methodologies
- Implement CI/CD pipelines using Docker and Kubernetes
- Ensure code quality through testing and code reviews

Required Skills:
- 5+ years of experience in full-stack development
- Proficiency in JavaScript, Python, and SQL
- Experience with React, Node.js, Express.js
- Knowledge of cloud platforms (AWS, Azure, or GCP)
- Familiarity with Git, Docker, and containerization
- Strong problem-solving and communication skills

Preferred Qualifications:
- Bachelor's degree in Computer Science or related field
- Experience with microservices architecture
- Knowledge of machine learning and data science
- Certification in cloud technologies
            ''',
            'location': 'San Francisco, CA',
            'employment_type': 'full-time',
            'experience_level': 'senior',
            'salary_range': '$120,000 - $180,000'
        },
        {
            'title': 'Product Manager - AI/ML',
            'company_name': 'DataTech Solutions',
            'job_description_text': '''
Join our innovative team as a Product Manager specializing in AI/ML products. You will drive the product strategy and roadmap for our machine learning platform.

Responsibilities:
- Define product vision and strategy for AI/ML products
- Collaborate with engineering teams to deliver high-quality features
- Conduct market research and competitive analysis
- Work with data scientists to understand technical requirements
- Manage product roadmap and prioritize features
- Analyze user feedback and product metrics

Requirements:
- 3+ years of product management experience
- Understanding of machine learning and data science concepts
- Experience with product analytics tools
- Strong analytical and problem-solving skills
- Excellent communication and leadership abilities
- Experience with Agile/Scrum methodologies

Nice to Have:
- Technical background in computer science or engineering
- Experience with Python, SQL, and data visualization tools
- Knowledge of cloud platforms and big data technologies
- MBA or advanced degree preferred
            ''',
            'location': 'New York, NY',
            'employment_type': 'full-time',
            'experience_level': 'mid-level',
            'salary_range': '$100,000 - $140,000'
        },
        {
            'title': 'Data Scientist',
            'company_name': 'Analytics Corp',
            'job_description_text': '''
We are looking for a talented Data Scientist to join our analytics team. You will work on cutting-edge machine learning projects and help drive data-driven decision making.

Key Duties:
- Develop and deploy machine learning models
- Analyze large datasets to extract insights
- Create data visualizations and reports
- Collaborate with business stakeholders
- Implement statistical analysis and A/B testing
- Build predictive models and recommendation systems

Required Skills:
- Master's degree in Data Science, Statistics, or related field
- 4+ years of experience in data science or analytics
- Proficiency in Python, R, and SQL
- Experience with machine learning libraries (scikit-learn, TensorFlow, PyTorch)
- Knowledge of statistical analysis and hypothesis testing
- Experience with data visualization tools (Tableau, Power BI, matplotlib)

Technical Requirements:
- Experience with big data technologies (Spark, Hadoop)
- Knowledge of cloud platforms (AWS, GCP, Azure)
- Familiarity with MLOps and model deployment
- Understanding of deep learning and neural networks
- Experience with version control (Git) and collaborative development
            ''',
            'location': 'Austin, TX',
            'employment_type': 'full-time',
            'experience_level': 'senior',
            'salary_range': '$110,000 - $160,000'
        }
    ]
    
    created_jds = []
    
    for i, jd in enumerate(jd_data):
        user = users[i % len(users)]  # Distribute among users
        
        job_desc = JobDescription.create_job_description(
            user_id=user.id,
            **jd
        )
        created_jds.append(job_desc)
    
    return created_jds

def create_sample_keywords(users, job_descriptions):
    """Create sample keywords for job descriptions"""
    # Keywords for Senior Full Stack Developer
    jd1_keywords = [
        ('react', 'technology', 0.95), ('node.js', 'technology', 0.95), ('python', 'technology', 0.95),
        ('javascript', 'technology', 0.90), ('postgresql', 'technology', 0.85), ('mongodb', 'technology', 0.85),
        ('docker', 'technology', 0.90), ('kubernetes', 'technology', 0.85), ('aws', 'technology', 0.80),
        ('full-stack development', 'skill', 0.90), ('restful apis', 'skill', 0.85), ('agile', 'methodology', 0.80),
        ('5+ years experience', 'experience', 0.95), ('senior level', 'experience', 0.90),
        ('computer science', 'education', 0.75), ('bachelor degree', 'education', 0.70)
    ]
    
    # Keywords for Product Manager
    jd2_keywords = [
        ('product management', 'skill', 0.95), ('machine learning', 'technology', 0.90), ('data science', 'technology', 0.85),
        ('python', 'technology', 0.75), ('sql', 'technology', 0.80), ('analytics', 'skill', 0.85),
        ('agile', 'methodology', 0.80), ('scrum', 'methodology', 0.75), ('product strategy', 'skill', 0.90),
        ('3+ years experience', 'experience', 0.90), ('mid-level', 'experience', 0.85),
        ('mba', 'education', 0.70), ('leadership', 'skill', 0.80)
    ]
    
    # Keywords for Data Scientist
    jd3_keywords = [
        ('data science', 'skill', 0.95), ('machine learning', 'technology', 0.95), ('python', 'technology', 0.95),
        ('r', 'technology', 0.85), ('sql', 'technology', 0.90), ('tensorflow', 'technology', 0.85),
        ('pytorch', 'technology', 0.80), ('scikit-learn', 'technology', 0.85), ('spark', 'technology', 0.75),
        ('statistics', 'skill', 0.90), ('deep learning', 'technology', 0.85), ('mlops', 'skill', 0.80),
        ('4+ years experience', 'experience', 0.90), ('senior level', 'experience', 0.85),
        ('master degree', 'education', 0.85), ('data science degree', 'education', 0.80)
    ]
    
    keyword_sets = [jd1_keywords, jd2_keywords, jd3_keywords]
    
    for i, jd in enumerate(job_descriptions):
        keywords = keyword_sets[i]
        for keyword, kw_type, confidence in keywords:
            kw = Keyword(
                user_id=jd.user_id,
                job_description_id=jd.id,
                keyword=keyword,
                keyword_type=kw_type,
                confidence_score=Decimal(str(confidence)),
                extraction_method='sample_data'
            )
            db.session.add(kw)
    
    db.session.commit()

def create_sample_data():
    """Create all sample data"""
    try:
        print("Creating sample subscription plans...")
        create_subscription_plans()
        
        print("Creating sample users...")
        users = create_sample_users()
        
        if users:
            print("Creating sample job descriptions...")
            job_descriptions = create_sample_job_descriptions(users)
            
            print("Creating sample keywords...")
            create_sample_keywords(users, job_descriptions)
            
            print("✅ Sample data created successfully!")
            print(f"Created {len(users)} users and {len(job_descriptions)} job descriptions")
        else:
            print("ℹ️  Sample users already exist, skipping data creation")
            
    except Exception as e:
        print(f"❌ Error creating sample data: {str(e)}")
        db.session.rollback()

if __name__ == '__main__':
    create_sample_data()
