/* Dr. Resume - Integrated Frontend Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    padding-top: 76px; /* Account for fixed navbar */
}

.main-content {
    min-height: calc(100vh - 76px);
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Page Management */
.page {
    padding: 2rem 0;
    min-height: calc(100vh - 76px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: calc(100vh - 76px);
    display: flex;
    align-items: center;
}

.hero-section .display-4 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.feature-highlights .feature-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.feature-highlights .feature-item i {
    font-size: 1.2rem;
}

.cta-buttons .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* Demo Card */
.demo-card {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.demo-card .card {
    border: none;
    border-radius: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.metric {
    padding: 1rem 0;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-weight: 500;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Progress Bars */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: var(--border-radius);
    transition: width 0.6s ease;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Loading States */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.file-upload-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Resume Cards */
.resume-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.resume-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.resume-status {
    font-size: 0.875rem;
    font-weight: 600;
}

.resume-status.processed {
    color: var(--success-color);
}

.resume-status.processing {
    color: var(--warning-color);
}

.resume-status.failed {
    color: var(--danger-color);
}

/* Job Description Cards */
.jd-card {
    transition: all 0.3s ease;
}

.jd-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.jd-meta {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* Matching Score Display */
.match-score {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
}

.match-score.excellent {
    color: var(--success-color);
}

.match-score.good {
    color: #28a745;
}

.match-score.fair {
    color: var(--warning-color);
}

.match-score.poor {
    color: #fd7e14;
}

.match-score.very-poor {
    color: var(--danger-color);
}

.match-grade {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

/* Keywords */
.keyword-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.keyword-tag.matched {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(25, 135, 84, 0.3);
}

.keyword-tag.missing {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.keyword-tag.extra {
    background-color: rgba(13, 202, 240, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(13, 202, 240, 0.3);
}

/* Suggestions */
.suggestion-card {
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.suggestion-card:hover {
    box-shadow: var(--box-shadow-lg);
}

.suggestion-card.high-priority {
    border-left-color: var(--danger-color);
}

.suggestion-card.medium-priority {
    border-left-color: var(--warning-color);
}

.suggestion-card.low-priority {
    border-left-color: var(--success-color);
}

.suggestion-priority {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dashboard Stats */
.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    background: white;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .hero-section .display-4 {
        font-size: 2.5rem;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .demo-card {
        margin-top: 2rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-strong {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.border-radius-lg {
    border-radius: 1rem !important;
}

.border-radius-xl {
    border-radius: 1.5rem !important;
}
