<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Resume - AI Resume Scanner</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showPage('home')">
                <i class="fas fa-user-md me-2"></i>
                Dr. Resume
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation (shown when logged in) -->
                <ul class="navbar-nav me-auto" id="mainNav" style="display: none;">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('dashboard')">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('upload')">
                            <i class="fas fa-upload me-1"></i>Upload Resume
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('job-descriptions')">
                            <i class="fas fa-briefcase me-1"></i>Job Descriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('matching')">
                            <i class="fas fa-chart-line me-1"></i>Matching
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('suggestions')">
                            <i class="fas fa-lightbulb me-1"></i>Suggestions
                        </a>
                    </li>
                </ul>
                
                <!-- User Menu (shown when logged in) -->
                <ul class="navbar-nav" id="userNav" style="display: none;">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <span id="userDisplayName">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showPage('account')">
                                <i class="fas fa-cog me-2"></i>Account Settings
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showPage('subscription')">
                                <i class="fas fa-crown me-2"></i>Subscription
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- Auth Navigation (shown when not logged in) -->
                <ul class="navbar-nav" id="authNav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('login')">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('register')">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Loading Spinner -->
        <div id="loadingSpinner" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading...</p>
        </div>

        <!-- Alert Container -->
        <div id="alertContainer" class="container mt-3"></div>

        <!-- Home Page -->
        <div id="homePage" class="page">
            <div class="hero-section">
                <div class="container">
                    <div class="row align-items-center min-vh-100">
                        <div class="col-lg-6">
                            <h1 class="display-4 fw-bold text-primary mb-4">
                                <i class="fas fa-user-md me-3"></i>
                                Dr. Resume
                            </h1>
                            <p class="lead mb-4">
                                AI-powered resume analysis that helps you optimize your resume for any job description. 
                                Get instant feedback, keyword suggestions, and matching scores.
                            </p>
                            
                            <div class="feature-highlights mb-5">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <i class="fas fa-chart-line text-success me-2"></i>
                                            <span>Instant Matching Scores</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <i class="fas fa-lightbulb text-warning me-2"></i>
                                            <span>AI-Powered Suggestions</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <i class="fas fa-keywords text-info me-2"></i>
                                            <span>Keyword Optimization</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <i class="fas fa-tachometer-alt text-primary me-2"></i>
                                            <span>Analytics Dashboard</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="cta-buttons">
                                <button class="btn btn-primary btn-lg me-3" onclick="showPage('register')">
                                    <i class="fas fa-rocket me-2"></i>
                                    Get Started Free
                                </button>
                                <button class="btn btn-outline-primary btn-lg" onclick="showPage('login')">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Sign In
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <div class="hero-image text-center">
                                <div class="demo-card">
                                    <div class="card shadow-lg">
                                        <div class="card-body">
                                            <h5 class="card-title">
                                                <i class="fas fa-file-alt text-primary me-2"></i>
                                                Resume Analysis
                                            </h5>
                                            <div class="progress mb-3">
                                                <div class="progress-bar bg-success" style="width: 85%">85% Match</div>
                                            </div>
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="metric">
                                                        <div class="metric-value text-success">24</div>
                                                        <div class="metric-label">Keywords</div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="metric">
                                                        <div class="metric-value text-warning">8</div>
                                                        <div class="metric-label">Missing</div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="metric">
                                                        <div class="metric-value text-info">12</div>
                                                        <div class="metric-label">Suggestions</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Page -->
        <div id="loginPage" class="page" style="display: none;">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-4">
                        <div class="card shadow">
                            <div class="card-body">
                                <h3 class="card-title text-center mb-4">
                                    <i class="fas fa-sign-in-alt text-primary me-2"></i>
                                    Sign In
                                </h3>
                                
                                <form id="loginForm">
                                    <div class="mb-3">
                                        <label for="loginEmail" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="loginEmail" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="loginPassword" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="loginPassword" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100" id="loginBtn">
                                        <span class="btn-text">Sign In</span>
                                        <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                                    </button>
                                </form>
                                
                                <div class="text-center mt-3">
                                    <p>Don't have an account? 
                                        <a href="#" onclick="showPage('register')">Register here</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Register Page -->
        <div id="registerPage" class="page" style="display: none;">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-5">
                        <div class="card shadow">
                            <div class="card-body">
                                <h3 class="card-title text-center mb-4">
                                    <i class="fas fa-user-plus text-primary me-2"></i>
                                    Create Account
                                </h3>
                                
                                <form id="registerForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="firstName">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="lastName">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="registerEmail" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="registerEmail" required>
                                        <div class="form-text" id="emailFeedback"></div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="registerPassword" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="registerPassword" required>
                                        <div class="form-text" id="passwordFeedback"></div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100" id="registerBtn">
                                        <span class="btn-text">Create Account</span>
                                        <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                                    </button>
                                </form>
                                
                                <div class="text-center mt-3">
                                    <p>Already have an account? 
                                        <a href="#" onclick="showPage('login')">Sign in here</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboardPage" class="page" style="display: none;">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                    <button class="btn btn-primary" onclick="showPage('upload')">
                        <i class="fas fa-plus me-2"></i>New Scan
                    </button>
                </div>
                
                <!-- Dashboard content will be loaded here -->
                <div id="dashboardContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading dashboard...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Page -->
        <div id="uploadPage" class="page" style="display: none;">
            <div class="container">
                <h2><i class="fas fa-upload me-2"></i>Upload Resume</h2>
                
                <!-- Upload content will be loaded here -->
                <div id="uploadContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading upload form...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Descriptions Page -->
        <div id="jobDescriptionsPage" class="page" style="display: none;">
            <div class="container">
                <h2><i class="fas fa-briefcase me-2"></i>Job Descriptions</h2>
                
                <!-- Job descriptions content will be loaded here -->
                <div id="jobDescriptionsContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading job descriptions...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other pages will be added dynamically -->
        <div id="matchingPage" class="page" style="display: none;"></div>
        <div id="suggestionsPage" class="page" style="display: none;"></div>
        <div id="accountPage" class="page" style="display: none;"></div>
        <div id="subscriptionPage" class="page" style="display: none;"></div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
