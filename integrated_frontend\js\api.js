/**
 * Dr. Resume - API Module
 * =======================
 * 
 * Handles all API requests to the integrated backend including resumes,
 * job descriptions, matching, suggestions, and dashboard data.
 */

class ApiClient {
    constructor() {
        this.baseUrl = DrResumeConfig.API_CONFIG.BASE_URL;
        this.timeout = DrResumeConfig.API_CONFIG.TIMEOUT;
        this.retryAttempts = DrResumeConfig.API_CONFIG.RETRY_ATTEMPTS;
        this.retryDelay = DrResumeConfig.API_CONFIG.RETRY_DELAY;
    }
    
    /**
     * Make HTTP request with retry logic and error handling
     */
    async makeRequest(url, options = {}, retryCount = 0) {
        try {
            // Add authentication headers
            const headers = {
                ...DrResumeAuth.getAuthHeaders(),
                ...options.headers
            };
            
            const requestOptions = {
                ...options,
                headers,
                timeout: this.timeout
            };
            
            const response = await fetch(url, requestOptions);
            
            // Handle authentication errors
            if (DrResumeAuth.handleAuthError(response)) {
                throw new Error('Authentication failed');
            }
            
            // Parse JSON response
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            return { success: true, data };
            
        } catch (error) {
            console.error(`API request failed (attempt ${retryCount + 1}):`, error);
            
            // Retry logic for network errors
            if (retryCount < this.retryAttempts && this.shouldRetry(error)) {
                await this.delay(this.retryDelay * (retryCount + 1));
                return this.makeRequest(url, options, retryCount + 1);
            }
            
            return { 
                success: false, 
                error: error.message || DrResumeConfig.ERROR_MESSAGES.NETWORK_ERROR 
            };
        }
    }
    
    /**
     * Determine if request should be retried
     */
    shouldRetry(error) {
        // Retry on network errors, but not on authentication or validation errors
        return !error.message.includes('Authentication') && 
               !error.message.includes('400') && 
               !error.message.includes('401') && 
               !error.message.includes('403');
    }
    
    /**
     * Delay utility for retry logic
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // ========================================
    // Resume API Methods (US-03)
    // ========================================
    
    /**
     * Upload resume file
     */
    async uploadResume(file, metadata = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        if (metadata.resume_title) {
            formData.append('resume_title', metadata.resume_title);
        }
        if (metadata.resume_description) {
            formData.append('resume_description', metadata.resume_description);
        }
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.RESUMES.UPLOAD);
        return this.makeRequest(url, {
            method: 'POST',
            body: formData,
            headers: {
                // Don't set Content-Type for FormData, let browser set it
                'Authorization': `Bearer ${DrResumeAuth.getAccessToken()}`
            }
        });
    }
    
    /**
     * Get list of user resumes
     */
    async getResumes(options = {}) {
        const params = new URLSearchParams();
        if (options.active_only !== undefined) {
            params.append('active_only', options.active_only);
        }
        if (options.include_content !== undefined) {
            params.append('include_content', options.include_content);
        }
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.RESUMES.LIST) + 
                   (params.toString() ? `?${params.toString()}` : '');
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get specific resume
     */
    async getResume(resumeId, includeContent = true) {
        const params = new URLSearchParams();
        params.append('include_content', includeContent);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.RESUMES.GET, { id: resumeId }) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Update resume metadata
     */
    async updateResume(resumeId, updateData) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.RESUMES.UPDATE, { id: resumeId });
        return this.makeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    }
    
    /**
     * Delete resume
     */
    async deleteResume(resumeId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.RESUMES.DELETE, { id: resumeId });
        return this.makeRequest(url, { method: 'DELETE' });
    }
    
    /**
     * Reprocess resume
     */
    async reprocessResume(resumeId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.RESUMES.REPROCESS, { id: resumeId });
        return this.makeRequest(url, { method: 'POST' });
    }
    
    // ========================================
    // Job Description API Methods (US-04)
    // ========================================
    
    /**
     * Create job description
     */
    async createJobDescription(jobData) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.JOB_DESCRIPTIONS.CREATE);
        return this.makeRequest(url, {
            method: 'POST',
            body: JSON.stringify(jobData)
        });
    }
    
    /**
     * Get list of job descriptions
     */
    async getJobDescriptions(options = {}) {
        const params = new URLSearchParams();
        if (options.active_only !== undefined) {
            params.append('active_only', options.active_only);
        }
        if (options.include_content !== undefined) {
            params.append('include_content', options.include_content);
        }
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.JOB_DESCRIPTIONS.LIST) + 
                   (params.toString() ? `?${params.toString()}` : '');
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get specific job description
     */
    async getJobDescription(jdId, includeContent = true) {
        const params = new URLSearchParams();
        params.append('include_content', includeContent);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.JOB_DESCRIPTIONS.GET, { id: jdId }) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Update job description
     */
    async updateJobDescription(jdId, updateData) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.JOB_DESCRIPTIONS.UPDATE, { id: jdId });
        return this.makeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    }
    
    /**
     * Delete job description
     */
    async deleteJobDescription(jdId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.JOB_DESCRIPTIONS.DELETE, { id: jdId });
        return this.makeRequest(url, { method: 'DELETE' });
    }
    
    // ========================================
    // Keywords API Methods (US-05)
    // ========================================
    
    /**
     * Get keywords for a document
     */
    async getDocumentKeywords(type, documentId, keywordType = null) {
        const params = new URLSearchParams();
        if (keywordType) {
            params.append('keyword_type', keywordType);
        }
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.KEYWORDS.GET_DOCUMENT, 
                               { type, id: documentId }) + 
                   (params.toString() ? `?${params.toString()}` : '');
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get keyword summary for a document
     */
    async getKeywordSummary(resumeId = null, jobDescriptionId = null) {
        const params = new URLSearchParams();
        if (resumeId) params.append('resume_id', resumeId);
        if (jobDescriptionId) params.append('job_description_id', jobDescriptionId);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.KEYWORDS.SUMMARY) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    // ========================================
    // Matching API Methods (US-06)
    // ========================================
    
    /**
     * Calculate matching score
     */
    async calculateMatching(resumeId, jobDescriptionId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.MATCHING.CALCULATE);
        return this.makeRequest(url, {
            method: 'POST',
            body: JSON.stringify({
                resume_id: resumeId,
                job_description_id: jobDescriptionId
            })
        });
    }
    
    /**
     * Get matching score
     */
    async getMatchingScore(matchingId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.MATCHING.GET, { id: matchingId });
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get list of matching scores
     */
    async getMatchingScores(resumeId = null, jobDescriptionId = null) {
        const params = new URLSearchParams();
        if (resumeId) params.append('resume_id', resumeId);
        if (jobDescriptionId) params.append('job_description_id', jobDescriptionId);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.MATCHING.LIST) + 
                   (params.toString() ? `?${params.toString()}` : '');
        return this.makeRequest(url, { method: 'GET' });
    }
    
    // ========================================
    // Suggestions API Methods (US-07)
    // ========================================
    
    /**
     * Generate basic suggestions
     */
    async generateBasicSuggestions(resumeId, jobDescriptionId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.SUGGESTIONS.BASIC);
        return this.makeRequest(url, {
            method: 'POST',
            body: JSON.stringify({
                resume_id: resumeId,
                job_description_id: jobDescriptionId
            })
        });
    }
    
    /**
     * Generate premium AI suggestions
     */
    async generatePremiumSuggestions(resumeId, jobDescriptionId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.SUGGESTIONS.PREMIUM);
        return this.makeRequest(url, {
            method: 'POST',
            body: JSON.stringify({
                resume_id: resumeId,
                job_description_id: jobDescriptionId
            })
        });
    }
    
    /**
     * Get suggestions list
     */
    async getSuggestions(resumeId = null, jobDescriptionId = null, activeOnly = true) {
        const params = new URLSearchParams();
        if (resumeId) params.append('resume_id', resumeId);
        if (jobDescriptionId) params.append('job_description_id', jobDescriptionId);
        params.append('active_only', activeOnly);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.SUGGESTIONS.LIST) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Update suggestion status
     */
    async updateSuggestion(suggestionId, updateData) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.SUGGESTIONS.UPDATE, { id: suggestionId });
        return this.makeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    }
    
    /**
     * Mark suggestion as implemented
     */
    async implementSuggestion(suggestionId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.SUGGESTIONS.IMPLEMENT, { id: suggestionId });
        return this.makeRequest(url, { method: 'POST' });
    }
    
    /**
     * Dismiss suggestion
     */
    async dismissSuggestion(suggestionId) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.SUGGESTIONS.DISMISS, { id: suggestionId });
        return this.makeRequest(url, { method: 'POST' });
    }
    
    // ========================================
    // Dashboard API Methods (US-08)
    // ========================================
    
    /**
     * Get dashboard overview
     */
    async getDashboardOverview() {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.DASHBOARD.OVERVIEW);
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get scan history
     */
    async getScanHistory(limit = 10, offset = 0) {
        const params = new URLSearchParams();
        params.append('limit', limit);
        params.append('offset', offset);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.DASHBOARD.HISTORY) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get analytics data
     */
    async getAnalytics(period = 'monthly') {
        const params = new URLSearchParams();
        params.append('period', period);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.DASHBOARD.ANALYTICS) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Get user activity
     */
    async getUserActivity(limit = 20) {
        const params = new URLSearchParams();
        params.append('limit', limit);
        
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.DASHBOARD.ACTIVITY) + 
                   `?${params.toString()}`;
        return this.makeRequest(url, { method: 'GET' });
    }
    
    // ========================================
    // Account API Methods (US-10)
    // ========================================
    
    /**
     * Get user profile
     */
    async getUserProfile() {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.ACCOUNT.PROFILE);
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Update user profile
     */
    async updateUserProfile(profileData) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.ACCOUNT.UPDATE);
        return this.makeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });
    }
    
    /**
     * Get user preferences
     */
    async getUserPreferences() {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.ACCOUNT.PREFERENCES);
        return this.makeRequest(url, { method: 'GET' });
    }
    
    /**
     * Update user preferences
     */
    async updateUserPreferences(preferences) {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.ACCOUNT.PREFERENCES);
        return this.makeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(preferences)
        });
    }
    
    /**
     * Get subscription information
     */
    async getSubscription() {
        const url = buildApiUrl(DrResumeConfig.API_ENDPOINTS.ACCOUNT.SUBSCRIPTION);
        return this.makeRequest(url, { method: 'GET' });
    }
}

// Create global API client instance
window.apiClient = new ApiClient();

// Export API methods for easy access
window.DrResumeAPI = {
    // Resumes
    uploadResume: (file, metadata) => window.apiClient.uploadResume(file, metadata),
    getResumes: (options) => window.apiClient.getResumes(options),
    getResume: (id, includeContent) => window.apiClient.getResume(id, includeContent),
    updateResume: (id, data) => window.apiClient.updateResume(id, data),
    deleteResume: (id) => window.apiClient.deleteResume(id),
    reprocessResume: (id) => window.apiClient.reprocessResume(id),
    
    // Job Descriptions
    createJobDescription: (data) => window.apiClient.createJobDescription(data),
    getJobDescriptions: (options) => window.apiClient.getJobDescriptions(options),
    getJobDescription: (id, includeContent) => window.apiClient.getJobDescription(id, includeContent),
    updateJobDescription: (id, data) => window.apiClient.updateJobDescription(id, data),
    deleteJobDescription: (id) => window.apiClient.deleteJobDescription(id),
    
    // Keywords
    getDocumentKeywords: (type, id, keywordType) => window.apiClient.getDocumentKeywords(type, id, keywordType),
    getKeywordSummary: (resumeId, jdId) => window.apiClient.getKeywordSummary(resumeId, jdId),
    
    // Matching
    calculateMatching: (resumeId, jdId) => window.apiClient.calculateMatching(resumeId, jdId),
    getMatchingScore: (id) => window.apiClient.getMatchingScore(id),
    getMatchingScores: (resumeId, jdId) => window.apiClient.getMatchingScores(resumeId, jdId),
    
    // Suggestions
    generateBasicSuggestions: (resumeId, jdId) => window.apiClient.generateBasicSuggestions(resumeId, jdId),
    generatePremiumSuggestions: (resumeId, jdId) => window.apiClient.generatePremiumSuggestions(resumeId, jdId),
    getSuggestions: (resumeId, jdId, activeOnly) => window.apiClient.getSuggestions(resumeId, jdId, activeOnly),
    updateSuggestion: (id, data) => window.apiClient.updateSuggestion(id, data),
    implementSuggestion: (id) => window.apiClient.implementSuggestion(id),
    dismissSuggestion: (id) => window.apiClient.dismissSuggestion(id),
    
    // Dashboard
    getDashboardOverview: () => window.apiClient.getDashboardOverview(),
    getScanHistory: (limit, offset) => window.apiClient.getScanHistory(limit, offset),
    getAnalytics: (period) => window.apiClient.getAnalytics(period),
    getUserActivity: (limit) => window.apiClient.getUserActivity(limit),
    
    // Account
    getUserProfile: () => window.apiClient.getUserProfile(),
    updateUserProfile: (data) => window.apiClient.updateUserProfile(data),
    getUserPreferences: () => window.apiClient.getUserPreferences(),
    updateUserPreferences: (data) => window.apiClient.updateUserPreferences(data),
    getSubscription: () => window.apiClient.getSubscription()
};

console.log('API module loaded');
