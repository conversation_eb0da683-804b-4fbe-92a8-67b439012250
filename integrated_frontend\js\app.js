/**
 * Dr. Resume - Main Application
 * ============================
 * 
 * Main application initialization and global functions.
 */

// Global application state
window.DrResumeApp = {
    initialized: false,
    currentUser: null,
    version: '2.0.0'
};

/**
 * Initialize the application
 */
async function initializeApp() {
    try {
        console.log('🚀 Initializing Dr. Resume Application...');
        
        // Check authentication status
        if (DrResumeAuth.isAuthenticated()) {
            console.log('✅ User is authenticated');
            
            // Validate token
            const validation = await DrResumeAuth.validateToken();
            if (validation.success) {
                DrResumeApp.currentUser = validation.user;
                DrResumeUI.showPage('dashboard');
            } else {
                console.log('❌ Token validation failed');
                DrResumeUI.showPage('home');
            }
        } else {
            console.log('ℹ️  User not authenticated');
            DrResumeUI.showPage('home');
        }
        
        // Update navigation
        DrResumeUI.updateNavigation();
        
        DrResumeApp.initialized = true;
        console.log('✅ Application initialized successfully');
        
    } catch (error) {
        console.error('❌ Application initialization failed:', error);
        DrResumeUI.showAlert('Application failed to initialize. Please refresh the page.', 'danger');
    }
}

/**
 * Global navigation function
 */
function showPage(pageId) {
    // Check authentication for protected pages
    const protectedPages = ['dashboard', 'upload', 'job-descriptions', 'matching', 'suggestions', 'account', 'subscription'];
    
    if (protectedPages.includes(pageId) && !DrResumeAuth.isAuthenticated()) {
        DrResumeUI.showAlert('Please log in to access this page.', 'warning');
        DrResumeUI.showPage('login');
        return;
    }
    
    DrResumeUI.showPage(pageId);
}

/**
 * Global logout function
 */
async function logout() {
    try {
        DrResumeUI.showLoading('Logging out...');
        
        const result = await DrResumeAuth.logout();
        
        if (result.success) {
            DrResumeApp.currentUser = null;
            DrResumeUI.showAlert(result.message, 'success');
            DrResumeUI.showPage('home');
            DrResumeUI.updateNavigation();
        }
    } catch (error) {
        console.error('Logout error:', error);
        DrResumeUI.showAlert('Logout failed. Please try again.', 'danger');
    } finally {
        DrResumeUI.hideLoading();
    }
}

/**
 * Handle email validation for registration
 */
async function validateEmail(email) {
    if (!email || !DrResumeConfig.APP_SETTINGS.VALIDATION.EMAIL_REGEX.test(email)) {
        return { valid: false, message: 'Please enter a valid email address' };
    }
    
    try {
        const result = await DrResumeAuth.checkEmail(email);
        
        if (result.success) {
            if (result.available) {
                return { valid: true, message: 'Email is available' };
            } else {
                return { valid: false, message: 'Email is already registered' };
            }
        } else {
            return { valid: false, message: result.message };
        }
    } catch (error) {
        return { valid: false, message: 'Unable to validate email' };
    }
}

/**
 * Handle password validation
 */
function validatePassword(password) {
    const minLength = DrResumeConfig.APP_SETTINGS.VALIDATION.PASSWORD_MIN_LENGTH;
    
    if (!password || password.length < minLength) {
        return { valid: false, message: `Password must be at least ${minLength} characters long` };
    }
    
    if (!/[A-Za-z]/.test(password)) {
        return { valid: false, message: 'Password must contain at least one letter' };
    }
    
    if (!/\d/.test(password)) {
        return { valid: false, message: 'Password must contain at least one number' };
    }
    
    return { valid: true, message: 'Password is valid' };
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format date for display
 */
function formatDate(dateString, options = {}) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return date.toLocaleDateString('en-US', { ...defaultOptions, ...options });
}

/**
 * Debounce function for input validation
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Copy text to clipboard
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        DrResumeUI.showAlert('Copied to clipboard!', 'success', 2000);
        return true;
    } catch (error) {
        console.error('Copy failed:', error);
        DrResumeUI.showAlert('Failed to copy to clipboard', 'danger', 2000);
        return false;
    }
}

/**
 * Download file from URL
 */
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * Get match score color class
 */
function getMatchScoreClass(score) {
    const thresholds = DrResumeConfig.APP_SETTINGS.MATCHING_THRESHOLDS;
    
    if (score >= thresholds.EXCELLENT) return 'excellent';
    if (score >= thresholds.GOOD) return 'good';
    if (score >= thresholds.FAIR) return 'fair';
    if (score >= thresholds.POOR) return 'poor';
    return 'very-poor';
}

/**
 * Get suggestion priority color
 */
function getSuggestionPriorityColor(priority) {
    const colors = {
        'critical': 'danger',
        'high': 'warning',
        'medium': 'info',
        'low': 'success'
    };
    return colors[priority] || 'secondary';
}

/**
 * Handle keyboard shortcuts
 */
function handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        // Focus search input if available
        const searchInput = document.querySelector('input[type="search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to close modals
    if (event.key === 'Escape') {
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }
}

/**
 * Set up real-time features (if enabled)
 */
function setupRealTimeFeatures() {
    if (!DrResumeConfig.FEATURE_FLAGS.REAL_TIME_UPDATES) {
        return;
    }
    
    // Placeholder for WebSocket or Server-Sent Events
    console.log('Real-time features would be initialized here');
}

/**
 * Handle application errors
 */
function handleGlobalError(error, context = 'Unknown') {
    console.error(`Global error in ${context}:`, error);
    
    // Don't show error alerts for network issues during development
    if (error.message && error.message.includes('fetch')) {
        return;
    }
    
    DrResumeUI.showAlert(
        'An unexpected error occurred. Please try again or contact support if the problem persists.',
        'danger'
    );
}

/**
 * Set up global error handling
 */
function setupErrorHandling() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        handleGlobalError(event.reason, 'Unhandled Promise Rejection');
    });
    
    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
        handleGlobalError(event.error, 'JavaScript Error');
    });
}

/**
 * Initialize application when DOM is ready
 */
document.addEventListener('DOMContentLoaded', async function() {
    console.log('📄 DOM Content Loaded');
    
    // Set up error handling
    setupErrorHandling();
    
    // Set up keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Set up real-time features
    setupRealTimeFeatures();
    
    // Initialize the application
    await initializeApp();
    
    // Set up periodic token refresh check
    setInterval(() => {
        if (DrResumeAuth.isAuthenticated()) {
            // Token refresh is handled automatically by AuthManager
        }
    }, 60000); // Check every minute
});

// Export global functions
window.showPage = showPage;
window.logout = logout;
window.validateEmail = validateEmail;
window.validatePassword = validatePassword;
window.formatFileSize = formatFileSize;
window.formatDate = formatDate;
window.debounce = debounce;
window.copyToClipboard = copyToClipboard;
window.downloadFile = downloadFile;
window.getMatchScoreClass = getMatchScoreClass;
window.getSuggestionPriorityColor = getSuggestionPriorityColor;

console.log('🎯 Dr. Resume Application Ready');
