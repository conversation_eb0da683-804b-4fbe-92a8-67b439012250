/**
 * Dr. Resume - Authentication Module
 * =================================
 * 
 * Handles user authentication including login, registration, token management,
 * and session handling for the integrated frontend application.
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.accessToken = null;
        this.refreshToken = null;
        this.refreshTimer = null;
        
        // Initialize from storage
        this.loadFromStorage();
        
        // Set up automatic token refresh
        this.setupTokenRefresh();
    }
    
    /**
     * Load authentication data from localStorage
     */
    loadFromStorage() {
        this.accessToken = getStorageItem(DrResumeConfig.STORAGE_KEYS.ACCESS_TOKEN);
        this.refreshToken = getStorageItem(DrResumeConfig.STORAGE_KEYS.REFRESH_TOKEN);
        this.currentUser = getStorageItem(DrResumeConfig.STORAGE_KEYS.USER_DATA);
        
        if (DrResumeConfig.FEATURE_FLAGS.DEBUG_MODE) {
            console.log('Auth data loaded from storage:', {
                hasAccessToken: !!this.accessToken,
                hasRefreshToken: !!this.refreshToken,
                hasUser: !!this.currentUser
            });
        }
    }
    
    /**
     * Save authentication data to localStorage
     */
    saveToStorage() {
        if (this.accessToken) {
            setStorageItem(DrResumeConfig.STORAGE_KEYS.ACCESS_TOKEN, this.accessToken);
        }
        if (this.refreshToken) {
            setStorageItem(DrResumeConfig.STORAGE_KEYS.REFRESH_TOKEN, this.refreshToken);
        }
        if (this.currentUser) {
            setStorageItem(DrResumeConfig.STORAGE_KEYS.USER_DATA, this.currentUser);
        }
    }
    
    /**
     * Clear authentication data
     */
    clearStorage() {
        removeStorageItem(DrResumeConfig.STORAGE_KEYS.ACCESS_TOKEN);
        removeStorageItem(DrResumeConfig.STORAGE_KEYS.REFRESH_TOKEN);
        removeStorageItem(DrResumeConfig.STORAGE_KEYS.USER_DATA);
        
        this.accessToken = null;
        this.refreshToken = null;
        this.currentUser = null;
        
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!(this.accessToken && this.currentUser);
    }
    
    /**
     * Get current user
     */
    getCurrentUser() {
        return this.currentUser;
    }
    
    /**
     * Get access token
     */
    getAccessToken() {
        return this.accessToken;
    }
    
    /**
     * Check if user has premium subscription
     */
    isPremiumUser() {
        return this.currentUser && this.currentUser.is_premium;
    }
    
    /**
     * Get user role
     */
    getUserRole() {
        return this.currentUser ? this.currentUser.role : 'guest';
    }
    
    /**
     * Register new user
     */
    async register(userData) {
        try {
            const response = await fetch(buildApiUrl(DrResumeConfig.API_ENDPOINTS.AUTH.REGISTER), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                return { success: true, user: result.user, message: result.message };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('Registration error:', error);
            return { 
                success: false, 
                message: DrResumeConfig.ERROR_MESSAGES.NETWORK_ERROR 
            };
        }
    }
    
    /**
     * Login user
     */
    async login(email, password) {
        try {
            const response = await fetch(buildApiUrl(DrResumeConfig.API_ENDPOINTS.AUTH.LOGIN), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Store authentication data
                this.accessToken = result.tokens.access_token;
                this.refreshToken = result.tokens.refresh_token;
                this.currentUser = result.user;
                
                // Save to storage
                this.saveToStorage();
                
                // Set up token refresh
                this.setupTokenRefresh(result.tokens.expires_in);
                
                return { 
                    success: true, 
                    user: result.user, 
                    message: result.message 
                };
            } else {
                return { success: false, message: result.message };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { 
                success: false, 
                message: DrResumeConfig.ERROR_MESSAGES.NETWORK_ERROR 
            };
        }
    }
    
    /**
     * Logout user
     */
    async logout() {
        try {
            // Call logout endpoint if we have a token
            if (this.accessToken) {
                await fetch(buildApiUrl(DrResumeConfig.API_ENDPOINTS.AUTH.LOGOUT), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error('Logout API error:', error);
            // Continue with local logout even if API call fails
        }
        
        // Clear local data
        this.clearStorage();
        
        return { success: true, message: DrResumeConfig.SUCCESS_MESSAGES.LOGOUT_SUCCESS };
    }
    
    /**
     * Refresh access token
     */
    async refreshAccessToken() {
        if (!this.refreshToken) {
            return { success: false, message: 'No refresh token available' };
        }
        
        try {
            const response = await fetch(buildApiUrl(DrResumeConfig.API_ENDPOINTS.AUTH.REFRESH), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.refreshToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.accessToken = result.access_token;
                setStorageItem(DrResumeConfig.STORAGE_KEYS.ACCESS_TOKEN, this.accessToken);
                
                // Set up next refresh
                this.setupTokenRefresh(result.expires_in);
                
                return { success: true };
            } else {
                // Refresh failed, need to re-login
                this.clearStorage();
                return { success: false, message: 'Session expired' };
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            this.clearStorage();
            return { success: false, message: 'Session expired' };
        }
    }
    
    /**
     * Set up automatic token refresh
     */
    setupTokenRefresh(expiresIn = 3600) {
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }
        
        // Refresh token 5 minutes before expiry
        const refreshTime = (expiresIn - 300) * 1000;
        
        if (refreshTime > 0) {
            this.refreshTimer = setTimeout(async () => {
                const result = await this.refreshAccessToken();
                if (!result.success) {
                    // Redirect to login if refresh fails
                    window.location.reload();
                }
            }, refreshTime);
        }
    }
    
    /**
     * Validate current token
     */
    async validateToken() {
        if (!this.accessToken) {
            return { success: false, message: 'No token available' };
        }
        
        try {
            const response = await fetch(buildApiUrl(DrResumeConfig.API_ENDPOINTS.AUTH.VALIDATE), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success && result.valid) {
                // Update user data if provided
                if (result.user) {
                    this.currentUser = result.user;
                    setStorageItem(DrResumeConfig.STORAGE_KEYS.USER_DATA, this.currentUser);
                }
                return { success: true, user: result.user };
            } else {
                this.clearStorage();
                return { success: false, message: 'Invalid token' };
            }
        } catch (error) {
            console.error('Token validation error:', error);
            return { success: false, message: 'Validation failed' };
        }
    }
    
    /**
     * Check email availability
     */
    async checkEmailAvailability(email) {
        try {
            const response = await fetch(buildApiUrl(DrResumeConfig.API_ENDPOINTS.AUTH.CHECK_EMAIL), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email })
            });
            
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Email check error:', error);
            return { 
                success: false, 
                message: DrResumeConfig.ERROR_MESSAGES.NETWORK_ERROR 
            };
        }
    }
    
    /**
     * Get authorization headers for API requests
     */
    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.accessToken) {
            headers['Authorization'] = `Bearer ${this.accessToken}`;
        }
        
        return headers;
    }
    
    /**
     * Handle authentication errors in API responses
     */
    handleAuthError(response) {
        if (response.status === 401) {
            // Unauthorized - clear auth data and redirect to login
            this.clearStorage();
            return true; // Indicates auth error was handled
        }
        return false;
    }
}

// Create global auth manager instance
window.authManager = new AuthManager();

// Export for use in other modules
window.DrResumeAuth = {
    register: (userData) => window.authManager.register(userData),
    login: (email, password) => window.authManager.login(email, password),
    logout: () => window.authManager.logout(),
    isAuthenticated: () => window.authManager.isAuthenticated(),
    getCurrentUser: () => window.authManager.getCurrentUser(),
    getAccessToken: () => window.authManager.getAccessToken(),
    isPremiumUser: () => window.authManager.isPremiumUser(),
    getUserRole: () => window.authManager.getUserRole(),
    refreshToken: () => window.authManager.refreshAccessToken(),
    validateToken: () => window.authManager.validateToken(),
    checkEmail: (email) => window.authManager.checkEmailAvailability(email),
    getAuthHeaders: () => window.authManager.getAuthHeaders(),
    handleAuthError: (response) => window.authManager.handleAuthError(response)
};

// Initialize authentication on page load
document.addEventListener('DOMContentLoaded', async function() {
    // Validate token if user appears to be logged in
    if (window.authManager.isAuthenticated()) {
        const validation = await window.authManager.validateToken();
        if (!validation.success) {
            console.log('Token validation failed, user will need to re-login');
        }
    }
});

console.log('Authentication module loaded');
