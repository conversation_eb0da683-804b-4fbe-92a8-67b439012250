/**
 * Dr. Resume - Configuration
 * ==========================
 * 
 * Global configuration settings for the integrated frontend application.
 * This file contains API endpoints, storage keys, and other constants.
 */

// API Configuration
const API_CONFIG = {
    BASE_URL: 'http://localhost:8000/api',
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 second
};

// API Endpoints
const API_ENDPOINTS = {
    // Authentication (US-01 & US-02)
    AUTH: {
        REGISTER: '/auth/register',
        LOGIN: '/auth/login',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        ME: '/auth/me',
        VALIDATE: '/auth/validate-token',
        CHECK_EMAIL: '/auth/check-email'
    },
    
    // Resumes (US-03)
    RESUMES: {
        LIST: '/resumes',
        UPLOAD: '/resumes',
        GET: '/resumes/{id}',
        UPDATE: '/resumes/{id}',
        DELETE: '/resumes/{id}',
        DOWNLOAD: '/resumes/{id}/download',
        REPROCESS: '/resumes/{id}/reprocess'
    },
    
    // Job Descriptions (US-04)
    JOB_DESCRIPTIONS: {
        LIST: '/job-descriptions',
        CREATE: '/job-descriptions',
        GET: '/job-descriptions/{id}',
        UPDATE: '/job-descriptions/{id}',
        DELETE: '/job-descriptions/{id}'
    },
    
    // Keywords (US-05)
    KEYWORDS: {
        LIST: '/keywords',
        GET_DOCUMENT: '/keywords/document/{type}/{id}',
        EXTRACT: '/keywords/extract',
        SUMMARY: '/keywords/summary'
    },
    
    // Matching (US-06)
    MATCHING: {
        CALCULATE: '/matching/calculate',
        GET: '/matching/{id}',
        LIST: '/matching',
        COMPARE: '/matching/compare'
    },
    
    // Suggestions (US-07)
    SUGGESTIONS: {
        BASIC: '/suggestions/basic',
        PREMIUM: '/suggestions/premium',
        LIST: '/suggestions',
        GET: '/suggestions/{id}',
        UPDATE: '/suggestions/{id}',
        IMPLEMENT: '/suggestions/{id}/implement',
        DISMISS: '/suggestions/{id}/dismiss'
    },
    
    // Dashboard (US-08)
    DASHBOARD: {
        OVERVIEW: '/dashboard/overview',
        HISTORY: '/dashboard/history',
        ANALYTICS: '/dashboard/analytics',
        ACTIVITY: '/dashboard/activity',
        EXPORT: '/dashboard/export'
    },
    
    // Security (US-09)
    SECURITY: {
        CHECK: '/security/check',
        REPORT: '/security/report',
        SETTINGS: '/security/settings'
    },
    
    // Account (US-10)
    ACCOUNT: {
        PROFILE: '/account/profile',
        UPDATE: '/account/update',
        PREFERENCES: '/account/preferences',
        SUBSCRIPTION: '/account/subscription',
        ACTIVITY: '/account/activity',
        DELETE: '/account/delete'
    }
};

// Local Storage Keys
const STORAGE_KEYS = {
    ACCESS_TOKEN: 'dr_resume_access_token',
    REFRESH_TOKEN: 'dr_resume_refresh_token',
    USER_DATA: 'dr_resume_user_data',
    PREFERENCES: 'dr_resume_preferences',
    THEME: 'dr_resume_theme',
    LANGUAGE: 'dr_resume_language',
    LAST_ACTIVITY: 'dr_resume_last_activity'
};

// Application Settings
const APP_SETTINGS = {
    NAME: 'Dr. Resume',
    VERSION: '2.0.0',
    DESCRIPTION: 'AI-Powered Resume Scanner',
    
    // File Upload Settings
    UPLOAD: {
        MAX_FILE_SIZE: 16 * 1024 * 1024, // 16MB
        ALLOWED_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx']
    },
    
    // UI Settings
    UI: {
        ITEMS_PER_PAGE: 10,
        DEBOUNCE_DELAY: 300,
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 5000,
        AUTO_SAVE_DELAY: 2000
    },
    
    // Validation Rules
    VALIDATION: {
        EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        PASSWORD_MIN_LENGTH: 6,
        NAME_MAX_LENGTH: 50,
        TITLE_MAX_LENGTH: 200,
        DESCRIPTION_MAX_LENGTH: 1000
    },
    
    // Chart Colors
    CHART_COLORS: {
        PRIMARY: '#0d6efd',
        SUCCESS: '#198754',
        WARNING: '#ffc107',
        DANGER: '#dc3545',
        INFO: '#0dcaf0',
        SECONDARY: '#6c757d',
        LIGHT: '#f8f9fa',
        DARK: '#212529'
    },
    
    // Matching Score Thresholds
    MATCHING_THRESHOLDS: {
        EXCELLENT: 90,
        GOOD: 80,
        FAIR: 70,
        POOR: 60
    },
    
    // Suggestion Priorities
    SUGGESTION_PRIORITIES: {
        CRITICAL: 'critical',
        HIGH: 'high',
        MEDIUM: 'medium',
        LOW: 'low'
    },
    
    // Subscription Plans
    SUBSCRIPTION_PLANS: {
        FREE: 'free',
        PREMIUM: 'premium',
        ENTERPRISE: 'enterprise'
    }
};

// Feature Flags
const FEATURE_FLAGS = {
    PREMIUM_SUGGESTIONS: true,
    AI_ANALYSIS: true,
    ADVANCED_ANALYTICS: true,
    EXPORT_FEATURES: true,
    REAL_TIME_UPDATES: false,
    BETA_FEATURES: false,
    DEBUG_MODE: false
};

// Error Messages
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your connection and try again.',
    UNAUTHORIZED: 'Your session has expired. Please log in again.',
    FORBIDDEN: 'You do not have permission to perform this action.',
    NOT_FOUND: 'The requested resource was not found.',
    SERVER_ERROR: 'Server error. Please try again later.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    FILE_TOO_LARGE: 'File is too large. Maximum size is 16MB.',
    INVALID_FILE_TYPE: 'Invalid file type. Only PDF, DOC, and DOCX files are allowed.',
    UPLOAD_FAILED: 'File upload failed. Please try again.',
    PROCESSING_FAILED: 'File processing failed. Please try again.',
    QUOTA_EXCEEDED: 'You have exceeded your usage quota. Please upgrade your plan.',
    RATE_LIMITED: 'Too many requests. Please wait a moment and try again.'
};

// Success Messages
const SUCCESS_MESSAGES = {
    LOGIN_SUCCESS: 'Login successful! Welcome back.',
    REGISTER_SUCCESS: 'Account created successfully! You can now log in.',
    LOGOUT_SUCCESS: 'Logged out successfully.',
    UPLOAD_SUCCESS: 'Resume uploaded successfully!',
    UPDATE_SUCCESS: 'Updated successfully!',
    DELETE_SUCCESS: 'Deleted successfully!',
    SAVE_SUCCESS: 'Saved successfully!',
    PROCESS_SUCCESS: 'Processing completed successfully!',
    EMAIL_SENT: 'Email sent successfully!',
    SUBSCRIPTION_UPDATED: 'Subscription updated successfully!'
};

// Loading Messages
const LOADING_MESSAGES = {
    LOGGING_IN: 'Signing you in...',
    REGISTERING: 'Creating your account...',
    UPLOADING: 'Uploading your resume...',
    PROCESSING: 'Processing your file...',
    ANALYZING: 'Analyzing your resume...',
    GENERATING_SUGGESTIONS: 'Generating suggestions...',
    CALCULATING_MATCH: 'Calculating match score...',
    LOADING_DASHBOARD: 'Loading your dashboard...',
    SAVING: 'Saving changes...',
    DELETING: 'Deleting...',
    UPDATING: 'Updating...'
};

// Theme Configuration
const THEME_CONFIG = {
    LIGHT: {
        name: 'light',
        displayName: 'Light Theme',
        primary: '#0d6efd',
        background: '#ffffff',
        surface: '#f8f9fa',
        text: '#212529'
    },
    DARK: {
        name: 'dark',
        displayName: 'Dark Theme',
        primary: '#0d6efd',
        background: '#212529',
        surface: '#343a40',
        text: '#ffffff'
    }
};

// Export configuration for use in other modules
window.DrResumeConfig = {
    API_CONFIG,
    API_ENDPOINTS,
    STORAGE_KEYS,
    APP_SETTINGS,
    FEATURE_FLAGS,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    LOADING_MESSAGES,
    THEME_CONFIG
};

// Utility function to build API URLs
window.buildApiUrl = function(endpoint, params = {}) {
    let url = API_CONFIG.BASE_URL + endpoint;
    
    // Replace path parameters
    for (const [key, value] of Object.entries(params)) {
        url = url.replace(`{${key}}`, encodeURIComponent(value));
    }
    
    return url;
};

// Utility function to get storage item with fallback
window.getStorageItem = function(key, fallback = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : fallback;
    } catch (error) {
        console.error('Error reading from localStorage:', error);
        return fallback;
    }
};

// Utility function to set storage item
window.setStorageItem = function(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.error('Error writing to localStorage:', error);
        return false;
    }
};

// Utility function to remove storage item
window.removeStorageItem = function(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error removing from localStorage:', error);
        return false;
    }
};

// Initialize configuration
console.log(`${APP_SETTINGS.NAME} v${APP_SETTINGS.VERSION} - Configuration loaded`);

// Debug mode logging
if (FEATURE_FLAGS.DEBUG_MODE) {
    console.log('Debug mode enabled');
    console.log('API Base URL:', API_CONFIG.BASE_URL);
    console.log('Feature flags:', FEATURE_FLAGS);
}
