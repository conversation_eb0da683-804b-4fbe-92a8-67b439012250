/**
 * Dr. Resume - UI Management Module
 * ================================
 * 
 * Handles UI interactions, page navigation, and dynamic content loading.
 */

class UIManager {
    constructor() {
        this.currentPage = 'home';
        this.isLoading = false;
        this.setupEventListeners();
    }
    
    /**
     * Set up global event listeners
     */
    setupEventListeners() {
        // Form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // File upload handling
        document.addEventListener('change', this.handleFileChange.bind(this));
        
        // Bootstrap modal events
        document.addEventListener('hidden.bs.modal', this.handleModalHidden.bind(this));
    }
    
    /**
     * Show specific page and hide others
     */
    showPage(pageId) {
        // Hide all pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.style.display = 'none';
        });
        
        // Show target page
        const targetPage = document.getElementById(pageId + 'Page');
        if (targetPage) {
            targetPage.style.display = 'block';
            this.currentPage = pageId;
            
            // Load page content if needed
            this.loadPageContent(pageId);
            
            // Update navigation
            this.updateNavigation();
        }
    }
    
    /**
     * Load dynamic content for specific pages
     */
    async loadPageContent(pageId) {
        switch (pageId) {
            case 'dashboard':
                await this.loadDashboard();
                break;
            case 'upload':
                await this.loadUploadPage();
                break;
            case 'job-descriptions':
                await this.loadJobDescriptions();
                break;
            case 'matching':
                await this.loadMatching();
                break;
            case 'suggestions':
                await this.loadSuggestions();
                break;
            case 'account':
                await this.loadAccount();
                break;
            case 'subscription':
                await this.loadSubscription();
                break;
        }
    }
    
    /**
     * Update navigation based on authentication status
     */
    updateNavigation() {
        const isAuthenticated = DrResumeAuth.isAuthenticated();
        const mainNav = document.getElementById('mainNav');
        const userNav = document.getElementById('userNav');
        const authNav = document.getElementById('authNav');
        const userDisplayName = document.getElementById('userDisplayName');
        
        if (isAuthenticated) {
            mainNav.style.display = 'flex';
            userNav.style.display = 'flex';
            authNav.style.display = 'none';
            
            const user = DrResumeAuth.getCurrentUser();
            if (user && userDisplayName) {
                userDisplayName.textContent = user.first_name || user.email.split('@')[0];
            }
        } else {
            mainNav.style.display = 'none';
            userNav.style.display = 'none';
            authNav.style.display = 'flex';
        }
    }
    
    /**
     * Show loading spinner
     */
    showLoading(message = 'Loading...') {
        this.isLoading = true;
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.style.display = 'block';
            const text = spinner.querySelector('p');
            if (text) text.textContent = message;
        }
    }
    
    /**
     * Hide loading spinner
     */
    hideLoading() {
        this.isLoading = false;
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
    }
    
    /**
     * Show alert message
     */
    showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;
        
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // Auto-dismiss after duration
        if (duration > 0) {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, duration);
        }
    }
    
    /**
     * Get icon for alert type
     */
    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    /**
     * Handle form submissions
     */
    async handleFormSubmit(event) {
        const form = event.target;
        
        if (form.id === 'loginForm') {
            event.preventDefault();
            await this.handleLogin(form);
        } else if (form.id === 'registerForm') {
            event.preventDefault();
            await this.handleRegister(form);
        }
    }
    
    /**
     * Handle login form submission
     */
    async handleLogin(form) {
        const email = form.querySelector('#loginEmail').value;
        const password = form.querySelector('#loginPassword').value;
        const submitBtn = form.querySelector('#loginBtn');
        
        this.setButtonLoading(submitBtn, true);
        
        try {
            const result = await DrResumeAuth.login(email, password);
            
            if (result.success) {
                this.showAlert(DrResumeConfig.SUCCESS_MESSAGES.LOGIN_SUCCESS, 'success');
                this.showPage('dashboard');
            } else {
                this.showAlert(result.message, 'danger');
            }
        } catch (error) {
            this.showAlert(DrResumeConfig.ERROR_MESSAGES.NETWORK_ERROR, 'danger');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }
    
    /**
     * Handle registration form submission
     */
    async handleRegister(form) {
        const formData = new FormData(form);
        const userData = {
            email: formData.get('email') || form.querySelector('#registerEmail').value,
            password: formData.get('password') || form.querySelector('#registerPassword').value,
            first_name: formData.get('first_name') || form.querySelector('#firstName').value,
            last_name: formData.get('last_name') || form.querySelector('#lastName').value
        };
        
        const submitBtn = form.querySelector('#registerBtn');
        this.setButtonLoading(submitBtn, true);
        
        try {
            const result = await DrResumeAuth.register(userData);
            
            if (result.success) {
                this.showAlert(DrResumeConfig.SUCCESS_MESSAGES.REGISTER_SUCCESS, 'success');
                this.showPage('login');
            } else {
                this.showAlert(result.message, 'danger');
            }
        } catch (error) {
            this.showAlert(DrResumeConfig.ERROR_MESSAGES.NETWORK_ERROR, 'danger');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }
    
    /**
     * Set button loading state
     */
    setButtonLoading(button, loading) {
        if (!button) return;
        
        const text = button.querySelector('.btn-text');
        const spinner = button.querySelector('.spinner-border');
        
        if (loading) {
            button.disabled = true;
            if (text) text.style.display = 'none';
            if (spinner) spinner.style.display = 'inline-block';
        } else {
            button.disabled = false;
            if (text) text.style.display = 'inline';
            if (spinner) spinner.style.display = 'none';
        }
    }
    
    /**
     * Handle file input changes
     */
    handleFileChange(event) {
        const input = event.target;
        
        if (input.type === 'file' && input.files.length > 0) {
            const file = input.files[0];
            this.validateFile(file, input);
        }
    }
    
    /**
     * Validate uploaded file
     */
    validateFile(file, input) {
        const maxSize = DrResumeConfig.APP_SETTINGS.UPLOAD.MAX_FILE_SIZE;
        const allowedTypes = DrResumeConfig.APP_SETTINGS.UPLOAD.ALLOWED_TYPES;
        
        // Check file size
        if (file.size > maxSize) {
            this.showAlert(DrResumeConfig.ERROR_MESSAGES.FILE_TOO_LARGE, 'danger');
            input.value = '';
            return false;
        }
        
        // Check file type
        if (!allowedTypes.includes(file.type)) {
            this.showAlert(DrResumeConfig.ERROR_MESSAGES.INVALID_FILE_TYPE, 'danger');
            input.value = '';
            return false;
        }
        
        return true;
    }
    
    /**
     * Handle modal hidden events
     */
    handleModalHidden(event) {
        // Reset forms when modals are closed
        const modal = event.target;
        const forms = modal.querySelectorAll('form');
        forms.forEach(form => form.reset());
    }
    
    /**
     * Load dashboard content
     */
    async loadDashboard() {
        const content = document.getElementById('dashboardContent');
        if (!content) return;
        
        try {
            const result = await DrResumeAPI.getDashboardOverview();
            
            if (result.success) {
                content.innerHTML = this.renderDashboard(result.data.overview);
            } else {
                content.innerHTML = '<div class="alert alert-danger">Failed to load dashboard</div>';
            }
        } catch (error) {
            content.innerHTML = '<div class="alert alert-danger">Error loading dashboard</div>';
        }
    }
    
    /**
     * Render dashboard HTML
     */
    renderDashboard(overview) {
        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-value text-primary">${overview.total_resumes}</div>
                        <div class="stat-label">Resumes</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="stat-value text-info">${overview.total_job_descriptions}</div>
                        <div class="stat-label">Job Descriptions</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-value text-success">${overview.total_scans}</div>
                        <div class="stat-label">Total Scans</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-value text-warning">${overview.average_match_score}%</div>
                        <div class="stat-label">Avg Match</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            ${this.renderRecentActivity(overview.recent_activity)}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-calendar me-2"></i>This Month</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="stat-value text-primary">${overview.monthly_scans}</div>
                            <div class="stat-label">Scans Completed</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render recent activity
     */
    renderRecentActivity(activities) {
        if (!activities || activities.length === 0) {
            return '<p class="text-muted">No recent activity</p>';
        }
        
        return activities.map(activity => `
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    <i class="fas fa-${this.getActivityIcon(activity.activity_type)} text-primary"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${activity.activity_title}</div>
                    <small class="text-muted">${this.formatDate(activity.created_at)}</small>
                </div>
            </div>
        `).join('');
    }
    
    /**
     * Get icon for activity type
     */
    getActivityIcon(type) {
        const icons = {
            'resume_upload': 'upload',
            'jd_create': 'briefcase',
            'scan_complete': 'chart-line',
            'suggestion_generated': 'lightbulb'
        };
        return icons[type] || 'circle';
    }
    
    /**
     * Format date for display
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
    
    /**
     * Load upload page content
     */
    async loadUploadPage() {
        const content = document.getElementById('uploadContent');
        if (!content) return;
        
        content.innerHTML = `
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Upload Your Resume</h5>
                            <p class="text-muted">Upload your resume to start analyzing and matching with job descriptions.</p>
                            
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="resumeFile" class="form-label">Resume File</label>
                                    <input type="file" class="form-control" id="resumeFile" name="file" accept=".pdf,.doc,.docx" required>
                                    <div class="form-text">Supported formats: PDF, DOC, DOCX (max 16MB)</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="resumeTitle" class="form-label">Resume Title</label>
                                    <input type="text" class="form-control" id="resumeTitle" name="resume_title" placeholder="e.g., Software Engineer Resume">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="resumeDescription" class="form-label">Description (Optional)</label>
                                    <textarea class="form-control" id="resumeDescription" name="resume_description" rows="3" placeholder="Brief description of this resume version"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>Upload Resume
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add upload form handler
        const uploadForm = document.getElementById('uploadForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', this.handleResumeUpload.bind(this));
        }
    }
    
    /**
     * Handle resume upload
     */
    async handleResumeUpload(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const file = formData.get('file');
        
        if (!file || file.size === 0) {
            this.showAlert('Please select a file to upload', 'danger');
            return;
        }
        
        const submitBtn = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);
        
        try {
            const metadata = {
                resume_title: formData.get('resume_title'),
                resume_description: formData.get('resume_description')
            };
            
            const result = await DrResumeAPI.uploadResume(file, metadata);
            
            if (result.success) {
                this.showAlert('Resume uploaded successfully!', 'success');
                form.reset();
                // Optionally redirect to dashboard or resume list
                setTimeout(() => this.showPage('dashboard'), 2000);
            } else {
                this.showAlert(result.error || 'Upload failed', 'danger');
            }
        } catch (error) {
            this.showAlert('Upload failed. Please try again.', 'danger');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }
    
    /**
     * Load job descriptions page
     */
    async loadJobDescriptions() {
        const content = document.getElementById('jobDescriptionsContent');
        if (!content) return;
        
        content.innerHTML = '<div class="text-center py-3"><div class="spinner-border"></div></div>';
        
        try {
            const result = await DrResumeAPI.getJobDescriptions();
            
            if (result.success) {
                content.innerHTML = this.renderJobDescriptions(result.data.job_descriptions);
            } else {
                content.innerHTML = '<div class="alert alert-danger">Failed to load job descriptions</div>';
            }
        } catch (error) {
            content.innerHTML = '<div class="alert alert-danger">Error loading job descriptions</div>';
        }
    }
    
    /**
     * Render job descriptions list
     */
    renderJobDescriptions(jobDescriptions) {
        if (!jobDescriptions || jobDescriptions.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                    <h5>No Job Descriptions Yet</h5>
                    <p class="text-muted">Create your first job description to start matching with resumes.</p>
                    <button class="btn btn-primary" onclick="uiManager.showCreateJDModal()">
                        <i class="fas fa-plus me-2"></i>Create Job Description
                    </button>
                </div>
            `;
        }
        
        return `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5>Your Job Descriptions</h5>
                <button class="btn btn-primary" onclick="uiManager.showCreateJDModal()">
                    <i class="fas fa-plus me-2"></i>Add New
                </button>
            </div>
            
            <div class="row">
                ${jobDescriptions.map(jd => `
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card jd-card h-100">
                            <div class="card-body">
                                <h6 class="card-title">${jd.title}</h6>
                                <p class="card-text text-muted">${jd.company_name || 'No company'}</p>
                                <div class="jd-meta">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>${jd.location || 'Remote'}
                                    </small><br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>${this.formatDate(jd.created_at)}
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="uiManager.viewJD('${jd.id}')">
                                    <i class="fas fa-eye me-1"></i>View
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="uiManager.editJD('${jd.id}')">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    /**
     * Placeholder methods for other pages
     */
    async loadMatching() {
        const content = document.getElementById('matchingPage');
        if (content) {
            content.innerHTML = '<div class="container"><h2>Matching Analysis</h2><p>Coming soon...</p></div>';
        }
    }
    
    async loadSuggestions() {
        const content = document.getElementById('suggestionsPage');
        if (content) {
            content.innerHTML = '<div class="container"><h2>AI Suggestions</h2><p>Coming soon...</p></div>';
        }
    }
    
    async loadAccount() {
        const content = document.getElementById('accountPage');
        if (content) {
            content.innerHTML = '<div class="container"><h2>Account Settings</h2><p>Coming soon...</p></div>';
        }
    }
    
    async loadSubscription() {
        const content = document.getElementById('subscriptionPage');
        if (content) {
            content.innerHTML = '<div class="container"><h2>Subscription</h2><p>Coming soon...</p></div>';
        }
    }
}

// Create global UI manager instance
window.uiManager = new UIManager();

// Export for use in other modules
window.DrResumeUI = {
    showPage: (pageId) => window.uiManager.showPage(pageId),
    showAlert: (message, type, duration) => window.uiManager.showAlert(message, type, duration),
    showLoading: (message) => window.uiManager.showLoading(message),
    hideLoading: () => window.uiManager.hideLoading(),
    updateNavigation: () => window.uiManager.updateNavigation()
};

console.log('UI module loaded');
